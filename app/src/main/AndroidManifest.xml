<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
    <!-- Internet permission for downloading wallpapers -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <!-- Storage permissions for saving wallpapers -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- Wallpaper permission for setting wallpapers -->
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    
    <!-- WiFi permission for network operations -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <queries
        android:packageNames="com.android.launcher, com.google.android.apps.nexuslauncher, com.google.android.launcher, com.google.android.apps.nexuslauncher, com.google.android.apps.pixel.launcher"
        tools:ignore="QueryAllPackagesPermission" />


    <application
        android:name=".ThemePackApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.ThemePack"
        tools:targetApi="31">
        
        <!-- Firebase configuration -->
        <!-- Disable Firebase auto-initialization to prevent crashes with placeholder config -->
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />
        <!-- Disable automatic Firebase initialization -->
        <meta-data
            android:name="firebase_analytics_collection_deactivated"
            android:value="true" />
        
        <!-- Disable Firebase Auto-Initialization -->
        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="${applicationId}.firebaseinitprovider"
            android:exported="false"
            android:initOrder="100"
            tools:node="remove" />
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.ThemePack">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.canhub.cropper.CropImageActivity"
            android:theme="@style/Base.Theme.AppCompat" />

        <activity
            android:name=".presentation.app_install.ShortcutProxyActivity"
            android:enabled="true"
            android:exported="true"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".presentation.customize_icon.ShortcutCreatedReceiver"
            android:exported="false" />

    </application>

</manifest>