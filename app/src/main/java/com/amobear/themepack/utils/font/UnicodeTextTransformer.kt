package com.amobear.themepack.utils.font

import com.amobear.themepack.presentation.app_install.AppInstallDataBundle
import com.amobear.themepack.utils.font.UnicodeTextTransformer.transformText
import kotlin.text.iterator

object UnicodeTextTransformer {

    enum class MathStyle(name: String) {
        NORMAL ("Normal"),
        BOLD ("Bold"),
        ITALIC ("Italic"),
        BOLD_ITALIC ("Bold Italic"),
        SCRIPT ("Script"),
        BOLD_SCRIPT ("Bold Script"),
        FRAKTUR ("Fraktur"),
        DOUBLE_STRUCK ("Double Struck"),
        BOLD_FRAKTUR ("Bold Fraktur"),
        SANS_SERIF ("Sans Serif"),
        SANS_SERIF_BOLD ("Sans Serif Bold"),
        SANS_SERIF_ITALIC ("Sans Serif Italic"),
        MONOSPACE ("Monospace");
    }

    fun MathStyle.transformText(text: String): String {
        val result = StringBuilder()

        for (char in text) {
            val transformedCodePoint = when (char) {
                in 'A'..'Z' -> transformUppercase(char, this@transformText)
                in 'a'..'z' -> transformLowercase(char, this@transformText)
                in '0'..'9' -> transformDigit(char, this@transformText)
                else -> char.code // Keep spaces and punctuation as-is
            }

            // Use String constructor that accepts code points
            if (transformedCodePoint > 65535) {
                result.append(String(intArrayOf(transformedCodePoint), 0, 1))
            } else {
                result.append(transformedCodePoint.toChar())
            }
        }

        return result.toString()
    }

    private fun transformUppercase(char: Char, style: MathStyle): Int {
        val baseOffset = char.code - 'A'.code

        // Handle special cases for certain styles
        return when (style) {
            MathStyle.NORMAL -> char.code
            MathStyle.BOLD -> 0x1D400 + baseOffset
            MathStyle.ITALIC -> {
                // Special case for 'h' in italic (missing in Unicode)
                if (char == 'H') 0x210B else 0x1D434 + baseOffset
            }

            MathStyle.BOLD_ITALIC -> 0x1D468 + baseOffset
            MathStyle.SCRIPT -> {
                // Script has several special mappings
                when (char) {
                    'B' -> 0x212C  // ℬ
                    'E' -> 0x2130  // ℰ
                    'F' -> 0x2131  // ℱ
                    'H' -> 0x210B  // ℋ
                    'I' -> 0x2110  // ℐ
                    'L' -> 0x2112  // ℒ
                    'M' -> 0x2133  // ℳ
                    'R' -> 0x211B  // ℛ
                    else -> 0x1D49C + baseOffset
                }
            }

            MathStyle.BOLD_SCRIPT -> 0x1D4D0 + baseOffset
            MathStyle.FRAKTUR -> {
                // Fraktur has special mappings
                when (char) {
                    'C' -> 0x212D  // ℭ
                    'H' -> 0x210C  // ℌ
                    'I' -> 0x2111  // ℑ
                    'R' -> 0x211C  // ℜ
                    'Z' -> 0x2128  // ℨ
                    else -> 0x1D504 + baseOffset
                }
            }

            MathStyle.DOUBLE_STRUCK -> {
                // Double-struck has special mappings
                when (char) {
                    'C' -> 0x2102  // ℂ
                    'H' -> 0x210D  // ℍ
                    'N' -> 0x2115  // ℕ
                    'P' -> 0x2119  // ℙ
                    'Q' -> 0x211A  // ℚ
                    'R' -> 0x211D  // ℝ
                    'Z' -> 0x2124  // ℤ
                    else -> 0x1D538 + baseOffset
                }
            }

            MathStyle.BOLD_FRAKTUR -> 0x1D56C + baseOffset
            MathStyle.SANS_SERIF -> 0x1D5A0 + baseOffset
            MathStyle.SANS_SERIF_BOLD -> 0x1D5D4 + baseOffset
            MathStyle.SANS_SERIF_ITALIC -> 0x1D608 + baseOffset
            MathStyle.MONOSPACE -> 0x1D670 + baseOffset
        }
    }

    private fun transformLowercase(char: Char, style: MathStyle): Int {
        val baseOffset = char.code - 'a'.code

        return when (style) {
            MathStyle.NORMAL -> char.code
            MathStyle.BOLD -> 0x1D41A + baseOffset
            MathStyle.ITALIC -> {
                // Special case for 'h' in italic
                if (char == 'h') 0x210E else 0x1D44E + baseOffset
            }

            MathStyle.BOLD_ITALIC -> 0x1D482 + baseOffset
            MathStyle.SCRIPT -> {
                // Script lowercase has special mappings
                when (char) {
                    'e' -> 0x212F  // ℯ
                    'g' -> 0x210A  // ℊ
                    'o' -> 0x2134  // ℴ
                    else -> 0x1D4B6 + baseOffset
                }
            }

            MathStyle.BOLD_SCRIPT -> 0x1D4EA + baseOffset
            MathStyle.FRAKTUR -> 0x1D51E + baseOffset
            MathStyle.DOUBLE_STRUCK -> 0x1D552 + baseOffset
            MathStyle.BOLD_FRAKTUR -> 0x1D586 + baseOffset
            MathStyle.SANS_SERIF -> 0x1D5BA + baseOffset
            MathStyle.SANS_SERIF_BOLD -> 0x1D5EE + baseOffset
            MathStyle.SANS_SERIF_ITALIC -> 0x1D622 + baseOffset
            MathStyle.MONOSPACE -> 0x1D68A + baseOffset
        }
    }

    private fun transformDigit(char: Char, style: MathStyle): Int {
        val baseOffset = char.code - '0'.code
        return when (style) {
            MathStyle.NORMAL -> char.code
            MathStyle.BOLD -> 0x1D7CE + baseOffset
            MathStyle.DOUBLE_STRUCK -> 0x1D7D8 + baseOffset
            MathStyle.SANS_SERIF -> 0x1D7E2 + baseOffset
            MathStyle.SANS_SERIF_BOLD -> 0x1D7EC + baseOffset
            MathStyle.MONOSPACE -> 0x1D7F6 + baseOffset
            else -> char.code
        }
    }

}

fun String.transformTextToSelectedFont() = AppInstallDataBundle.customizedIconState?.fonts?.firstOrNull { it.isSelected }?.fontStyle?.transformText(this) ?: UnicodeTextTransformer.MathStyle.NORMAL.transformText(this)