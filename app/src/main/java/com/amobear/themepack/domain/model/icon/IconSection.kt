package com.amobear.themepack.domain.model.icon

import androidx.compose.runtime.Immutable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.collections.immutable.ImmutableList


@Immutable
@JsonClass(generateAdapter = true)
data class IconSection(
    @Json(name = "title") val title: String,
    @Json(name = "icons") val icons: List<IconItem>,
    @Json(name = "hasMore") val hasMore: Boolean = true
)