package com.amobear.themepack.domain.usecase

import com.amobear.themepack.core.AppCoroutineDispatchers
import com.amobear.themepack.domain.model.icon.CategoryItem
import com.amobear.themepack.domain.repository.IconsRepository
import kotlinx.collections.immutable.ImmutableList
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetCategoriesUseCase @Inject constructor(
    private val iconsRepository: IconsRepository,
    private val dispatcher: AppCoroutineDispatchers,
) {
    suspend operator fun invoke(): Result<ImmutableList<CategoryItem>> = withContext(dispatcher.io) {
        return@withContext iconsRepository.getCategories()
    }
}