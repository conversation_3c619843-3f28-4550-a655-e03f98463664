package com.amobear.themepack.domain.usecase

import com.amobear.themepack.core.AppCoroutineDispatchers
import com.amobear.themepack.domain.model.icon.IconSection
import com.amobear.themepack.domain.repository.IconsRepository
import kotlinx.collections.immutable.ImmutableList
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetIconSectionsUseCase @Inject constructor(
    private val iconsRepository: IconsRepository,
    private val dispatchers: AppCoroutineDispatchers,
) {
    suspend operator fun invoke(categoryId: String? = null): Result<ImmutableList<IconSection>>  =
        withContext(dispatchers.io) {
            return@withContext if (categoryId != null) {
                iconsRepository.getIconSections(categoryId)
            } else {
                iconsRepository.getAllIconSections()
            }
        }
}