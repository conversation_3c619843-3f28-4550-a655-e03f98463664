package com.amobear.themepack.domain.model.icon

import androidx.compose.runtime.Immutable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@Immutable
@JsonClass(generateAdapter = true)
data class IconItem(
    @Json(name = "id") val id: String,
    @<PERSON><PERSON>(name = "imageUrl") val imageUrl: String? = null,
    @<PERSON><PERSON>(name = "name") val name: String = "",
    @<PERSON><PERSON>(name = "appPackageName") val appPackageName: String = "",
    @<PERSON>son(name = "category") val category: String,
    @<PERSON><PERSON>(name = "isSelected") val isSelected: Boolean = false
)