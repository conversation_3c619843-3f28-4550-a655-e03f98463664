package com.amobear.themepack.domain.model.app

import android.graphics.drawable.Drawable

data class AppItem(
    val name: String = "",
    val customIconUrl: String ="",  // Base64 encoded source icon
    val destinationDrawable: Drawable?,  // Base64 encoded destination icon (null if not available)
    val packageName: String ="",  // Package name for installation purposes
    val isSelected: Boolean = false,
    val appState: AppState = AppState.NOT_INSTALLED_AD_REQUIRED
)

enum class AppState {
    NOT_INSTALLED_DIRECT,  // Not installed, no ad required
    NOT_INSTALLED_AD_REQUIRED,  // Not installed, requires watching an ad
    INSTALLED  // Already installed
}