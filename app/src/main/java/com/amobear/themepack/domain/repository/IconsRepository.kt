package com.amobear.themepack.domain.repository

import com.amobear.themepack.domain.model.icon.CategoryItem
import com.amobear.themepack.domain.model.icon.IconSection
import kotlinx.collections.immutable.ImmutableList

interface IconsRepository {
    suspend fun getCategories(): Result<ImmutableList<CategoryItem>>
    suspend fun getIconSections(categoryId: String): Result<ImmutableList<IconSection>>
    suspend fun getAllIconSections(): Result<ImmutableList<IconSection>>
}