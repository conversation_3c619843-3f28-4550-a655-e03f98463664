package com.amobear.themepack.extension

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.util.Base64
import android.util.Log
import timber.log.Timber
import java.io.ByteArrayOutputStream
import androidx.core.graphics.createBitmap
import androidx.core.graphics.drawable.toBitmap
import androidx.core.net.toUri
import coil.ImageLoader
import coil.request.ImageRequest

private val escapedRegex = """\\([\\;,":])""".toRegex()

val String.Companion.EMPTY
  get() = ""

fun String.unescape(): String = replace(escapedRegex) { escaped ->
  escaped.groupValues[1]
}

fun String.removePrefixIgnoreCase(prefix: String): String = substring(prefix.length)

fun String.startsWithIgnoreCase(prefix: String): Boolean = startsWith(prefix, true)

fun String.startsWithAnyIgnoreCase(prefixes: List<String>): Boolean {
  prefixes.forEach { prefix ->
    if (startsWith(prefix, true)) {
      return true
    }
  }
  return false
}

fun String.toUriThenBitmap(context: Context): Bitmap {
  return this.toUri().toBitmap(context)
}

suspend fun String.loadImageAsBitmap(context: Context) = runCatching {
  val imageLoader = ImageLoader(context)
  imageLoader.execute(
    ImageRequest.Builder(context)
      .data(this)
      .build()
  ).drawable?.toBitmap()
}.getOrElse { e ->
  Timber.tag("ImageLoad").e("Failed to load image: ${e.message}")
  null
}

fun List<String?>.joinToStringNotNullOrBlank(separator: String): String = filter {
  it.isNullOrBlank().not()
}.joinToString(separator)

// Extension function to convert base64 string to bitmap
fun String?.toBitmap(): Bitmap? {
  if (this.isNullOrEmpty()) return null

  return try {
    val decodedBytes = Base64.decode(this, Base64.DEFAULT)
    BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
  } catch (e: Exception) {
      Timber.tag("Base64Utils").e(e, "Error converting base64 to bitmap")
    null
  }
}

// For Bitmap to base64 - this can't be a String extension as it operates on Bitmap
// Instead making it a Bitmap extension function
fun Bitmap.toBase64(): String {
  return try {
    val outputStream = ByteArrayOutputStream()
    this.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
    val byteArray = outputStream.toByteArray()
    Base64.encodeToString(byteArray, Base64.DEFAULT)
  } catch (e: Exception) {
      Timber.tag("Base64Utils").e(e, "Error converting bitmap to base64")
    ""
  }
}

// For getting app icon - making this a String (packageName) extension
fun String.getAppIconAsBase64(context: Context): String? {
  return try {
    val drawable = context.packageManager.getApplicationIcon(this) // 'this' is the packageName

    // Convert drawable to bitmap
    val bitmap = if (drawable is BitmapDrawable) {
      drawable.bitmap
    } else {
      val width = drawable.intrinsicWidth.takeIf { it > 0 } ?: 256
      val height = drawable.intrinsicHeight.takeIf { it > 0 } ?: 256

      val bitmap = createBitmap(width, height)
      val canvas = Canvas(bitmap)
      drawable.setBounds(0, 0, canvas.width, canvas.height)
      drawable.draw(canvas)
      bitmap
    }

    bitmap.toBase64()
  } catch (e: Exception) {
      Timber.tag("Base64Utils").e(e, "Error getting app icon")
    null
  }
}


fun Drawable.toBase64(): String? {
  return try {
    val bitmap = if (this is BitmapDrawable) {
      this.bitmap
    } else {
      val width = this.intrinsicWidth.takeIf { it > 0 } ?: 256
      val height = this.intrinsicHeight.takeIf { it > 0 } ?: 256

      val bitmap = createBitmap(width, height)
      val canvas = Canvas(bitmap)
      this.setBounds(0, 0, canvas.width, canvas.height)
      this.draw(canvas)
      bitmap
    }

    bitmap.toBase64()
  } catch (e: Exception) {
      Timber.tag("Base64Utils").e(e, "Error converting drawable to base64")
    null
  }
}