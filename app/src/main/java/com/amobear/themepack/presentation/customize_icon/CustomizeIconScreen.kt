package com.amobear.themepack.presentation.customize_icon

import com.amobear.themepack.utils.font.UnicodeTextTransformer.transformText
import android.graphics.Bitmap
import android.net.Uri
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.amobear.themepack.R
import com.amobear.themepack.designsystem.ThemeAppTheme
import com.amobear.themepack.extension.toBitmap
import com.amobear.themepack.presentation.app_install.AppInstallDataBundle
import com.canhub.cropper.CropImageContract
import com.canhub.cropper.CropImageContractOptions
import com.canhub.cropper.CropImageOptions
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlin.math.roundToInt

@Composable
fun CustomizeIconRoute(
    onNavigateBack: () -> Unit,
    onNavigateToAppIconChanging: () -> Unit,
    viewModel: CustomizeIconViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val imageCropLauncher = rememberLauncherForActivityResult(CropImageContract()) { result ->
        if (result.isSuccessful) {
            viewModel.processIntent(
                CustomizeIconIntent.SelectBackgroundImage(
                    result.uriContent?.toBitmap(context) ?: run {
                        Toast.makeText(context, "Failed to get image", Toast.LENGTH_SHORT).show()
                        return@rememberLauncherForActivityResult
                    }
                ))
        } else {
            // an error occurred cropping
            val exception = result.error
            exception?.let {
                Toast.makeText(context, "Error cropping image: ${it.message}", Toast.LENGTH_SHORT)
                    .show()
            }
        }
    }

    val imagePickerLauncher =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.GetContent()) { uri: Uri? ->
            val cropOptions = CropImageContractOptions(uri, CropImageOptions())
            imageCropLauncher.launch(cropOptions)
        }

    LaunchedEffect(state.errorMessage) {
        state.errorMessage?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
        }
    }

    CustomizeIconScreen(
        state = state,
        onIntent = { intent ->
            when (intent) {
                is CustomizeIconIntent.OnChooseImage -> {
//                    imagePicker.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
                    imagePickerLauncher.launch("image/*")
                }

                is CustomizeIconIntent.SaveAndExit -> {
                    AppInstallDataBundle.customizedIconState = state
                    onNavigateToAppIconChanging()
                }

                else -> viewModel.processIntent(intent)
            }
        },
        onNavigateBack = onNavigateBack,
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomizeIconScreen(
    state: CustomizeIconState,
    onIntent: (CustomizeIconIntent) -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val onIntentState by rememberUpdatedState(onIntent)
    val onNavigateBackState by rememberUpdatedState(onNavigateBack)

    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(
                title = { Text("Customize the icon") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBackState) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = ThemeAppTheme.color.background,
                    titleContentColor = Color.Black
                ),
                actions = {
                    IconButton(
                        onClick = { onIntentState(CustomizeIconIntent.SaveAndExit) },
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            tint = Color.Blue,
                            contentDescription = "Save and exit"
                        )
                    }
                }
            )
        },
        containerColor = Color(0xFFF5F5F5)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
                .verticalScroll(rememberScrollState())
                .padding(
                    top = paddingValues.calculateTopPadding(),
                    bottom = 16.dp + paddingValues.calculateBottomPadding()
                ),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            IconSelectionSection(
                iconColor = state.selectedSymbolColor,
                backgroundColor = state.selectedBackgroundColor,
                backgroundGradient = if (state.backgroundColorMode == BackgroundColorMode.GRADIENT) {
                    state.backgroundGradient
                } else null,
                backgroundBitmap = state.backgroundImage,
                fontOption = state.fonts.find { it.isSelected } ?: FontOption.defaultFont,
            )

            Spacer(
                modifier = Modifier
                    .height(16.dp)
                    .fillMaxWidth()
                    .background(Color(0xFFF6F2F2))
            )

            SectionWithTitle(title = "Icon font") {
                FontSelector(
                    fonts = state.fonts,
                    onSelectFont = { onIntentState(CustomizeIconIntent.SelectFont(it)) },
                )
            }

            SectionWithTitle(title = "Symbol color") {
                ColorPicker(
                    colors = defaultColors,
                    onColorSelected = { color ->
                        onIntentState(CustomizeIconIntent.SelectSymbolColor(color))
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                TransparentSlider(
                    progress = state.symbolColorAlpha,
                    onProgressChanged = {
                        onIntentState(
                            CustomizeIconIntent.SetSymbolColorGradient(
                                it
                            )
                        )
                    },
                    baseColor = state.selectedSymbolColor.copy(alpha = state.symbolColorAlpha.roundToInt() / 100f),
                )
            }

            BackgroundColorSection(
                gradientProgress = state.backgroundColorAlpha,
                currentColor = state.selectedBackgroundColor,
                backgroundMode = state.backgroundColorMode,
                onSelectColor = { onIntentState(CustomizeIconIntent.SelectBackgroundColor(it)) },
                onSelectGradient = { onIntentState(CustomizeIconIntent.SelectBackgroundGradient(it)) },
                onListColorSelected = { onIntentState(CustomizeIconIntent.SelectGradientColorList(it)) },
                onGradientChanged = {
                    onIntentState(
                        CustomizeIconIntent.SetBackgroundColorGradient(
                            it
                        )
                    )
                },
                onSwitchMode = { onIntentState(CustomizeIconIntent.SwitchBackgroundColorMode) }
            )

            ImportFromGalleryButton(
                modifier = Modifier.padding(horizontal = 16.dp),
                onClick = { onIntentState(CustomizeIconIntent.OnChooseImage) }
            )
        }
    }
}

@Composable
fun IconSelectionSection(
    fontOption: FontOption,
    iconColor: Color,
    backgroundColor: Color,
    backgroundGradient: Brush? = null,
    backgroundBitmap: Bitmap? = null,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        IconOption(
            icon = Icons.Default.Call,
            label = "Phone",
            iconColor = iconColor,
            backgroundColor = backgroundColor,
            backgroundGradient = backgroundGradient,
            backgroundBitmap = backgroundBitmap,
            fontOption = fontOption,
        )

        IconOption(
            icon = Icons.Default.Email,
            label = "Mail",
            iconColor = iconColor,
            backgroundColor = backgroundColor,
            backgroundGradient = backgroundGradient,
            backgroundBitmap = backgroundBitmap,
            fontOption = fontOption,
        )

        IconOption(
            icon = Icons.Default.Settings,
            label = "Settings",
            iconColor = iconColor,
            backgroundColor = backgroundColor,
            backgroundGradient = backgroundGradient,
            backgroundBitmap = backgroundBitmap,
            fontOption = fontOption,
        )
    }
}

@Composable
fun IconOption(
    icon: ImageVector,
    label: String,
    iconColor: Color,
    backgroundColor: Color,
    fontOption: FontOption,
    backgroundGradient: Brush? = null,
    backgroundBitmap: Bitmap? = null,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            modifier = modifier
                .size(64.dp)
                .then(
                    if (backgroundGradient != null) {
                        Modifier.background(
                            brush = backgroundGradient,
                            shape = RoundedCornerShape(16.dp)
                        )
                    } else {
                        Modifier.background(
                            color = backgroundColor,
                            shape = RoundedCornerShape(16.dp)
                        )
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            if (backgroundBitmap != null) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(backgroundBitmap)
                        .crossfade(true)
                        .build(),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.fillMaxSize()
                )
            }
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = iconColor,
                modifier = Modifier.size(32.dp)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = fontOption.fontStyle.transformText(text = label),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun SectionWithTitle(
    title: String,
    modifier: Modifier = Modifier,
    trailingIcon: @Composable () -> Unit = {},
    content: @Composable () -> Unit = {}
) {
    Column(modifier = modifier.padding(horizontal = 16.dp)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )

            trailingIcon()
        }

        Spacer(modifier = Modifier.height(8.dp))

        content()
    }
}

@Composable
fun FontSelector(
    fonts: ImmutableList<FontOption>,
    onSelectFont: (String) -> Unit,
    backgroundColor: Color = Color(0xFFF6F2F2),
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(fonts) { font ->
            FontItem(
                modifier = Modifier.size(48.dp),
                fontStyle = font,
                onClick = { onSelectFont(font.fontStyle.name) },
                backgroundColor = backgroundColor
            )
        }
    }
}

@Composable
fun FontItem(
    fontStyle: FontOption,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color(0xFFF6F2F2),
) {
    val borderColor by animateColorAsState(
        targetValue = if (fontStyle.isSelected) Color.Blue else Color.Transparent,
        label = "borderColor"
    )
    val borderStrokeState by animateDpAsState(
        targetValue = if (fontStyle.isSelected) 2.dp else 0.dp,
        label = "borderWidth"
    )

    val interactionSource = remember { MutableInteractionSource() }

    Surface(
        shape = CircleShape,
        border = BorderStroke(
            width = borderStrokeState,
            color = borderColor
        ),
        color = backgroundColor,
        modifier = modifier.clickable(
            interactionSource = interactionSource,
            indication = rememberRipple(bounded = false, radius = 24.dp),
            onClick = onClick
        ),
    ) {
        Box(
            modifier = Modifier
                .defaultMinSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = fontStyle.fontStyle.transformText("abc"),
                color = Color.Black,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Composable
fun TransparentSlider(
    progress: Float,
    baseColor: Color,
    backgroundBrush: Brush? = null,
    onProgressChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    val currentOnProgressChanged by rememberUpdatedState(onProgressChanged)
    val density = LocalDensity.current

    val sliderInteraction = remember(density) {
        object {
            fun handleDrag(size: Size, offset: Offset): Float {
                val dragPosition = offset.x.coerceIn(0f, size.width)
                return (dragPosition / size.width).coerceIn(0f, 1f)
            }

            fun drawIndicator(drawScope: DrawScope, progress: Float, baseColor: Color) {
                with(drawScope) {
                    val indicatorX = progress.coerceIn(0f, 1f) * size.width
                    val indicatorY = size.height / 2f
                    val center = Offset(indicatorX, indicatorY)

                    val radiusPx = with(density) { 12.dp.toPx() }
                    val strokeWidthPx = with(density) { 2.dp.toPx() }

                    drawCircle(
                        color = Color.White,
                        radius = radiusPx + strokeWidthPx,
                        center = center
                    )

                    drawCircle(
                        color = baseColor.copy(alpha = 1f),
                        radius = radiusPx,
                        center = center
                    )
                }
            }
        }
    }

    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Surface(
            modifier = Modifier
                .weight(1f)
                .height(32.dp),
            shape = RoundedCornerShape(16.dp),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .drawWithCache {
                        onDrawBehind {
                            // Checkerboard pattern for transparency visualization
                            val tileSize = 16f
                            val tileCount = (size.width / tileSize).toInt()
                            val darkColor = Color.hsl(0f, 0f, 0.8f)
                            val lightColor = Color.hsl(1f, 1f, 1f)
                            for (i in 0..tileCount) {
                                for (j in 0..tileCount) {
                                    drawRect(
                                        topLeft = Offset(i * tileSize, j * tileSize),
                                        color = if ((i + j) % 2 == 0) darkColor else lightColor,
                                        size = Size(tileSize, tileSize)
                                    )
                                }
                            }
                        }
                    }
                    .then(
                        if (backgroundBrush != null) {
                            // If we have a gradient, use it for the background
                            Modifier.background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        Color.Black.copy(alpha = 1f)
                                    ),
                                    startX = 0f,
                                    endX = Float.POSITIVE_INFINITY
                                ),
                                alpha = progress
                            )
                        } else {
                            // Default solid color background
                            Modifier.background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(Color.Transparent, baseColor.copy(alpha = 1f))
                                )
                            )
                        }
                    )
                    .padding(horizontal = 4.dp, vertical = 4.dp)
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragStart = { offset ->
                                val newProgress = sliderInteraction.handleDrag(
                                    size = size.toSize(),
                                    offset = offset
                                )
                                currentOnProgressChanged(newProgress)
                            },
                            onDrag = { change, _ ->
                                val newProgress = sliderInteraction.handleDrag(
                                    size = size.toSize(),
                                    offset = change.position
                                )
                                currentOnProgressChanged(newProgress)
                                change.consume()
                            }
                        )
                    }
            ) {
                Canvas(modifier = Modifier.fillMaxSize()) {
                    sliderInteraction.drawIndicator(this, progress, baseColor)
                }
            }
        }
    }
}

@Composable
fun BackgroundColorSection(
    gradientProgress: Float,
    currentColor: Color,
    onSelectColor: (Color) -> Unit,
    onSelectGradient: (Brush) -> Unit,
    onGradientChanged: (Float) -> Unit,
    backgroundMode: BackgroundColorMode,
    onSwitchMode: () -> Unit,
    onListColorSelected: ((List<Color>) -> Unit)? = null,
) {
    val onSwitchModeRemembered by rememberUpdatedState(onSwitchMode)

    val currentGradient = remember(backgroundMode) {
        mutableStateOf<Brush?>(null)
    }

    val onGradientSelectedCallback = remember(onSelectGradient) {
        { brush: Brush ->
            currentGradient.value = brush
            onSelectGradient(brush)
        }
    }

    SectionWithTitle(
        title = "Background color",
        trailingIcon = {
            Text(
                text = when (backgroundMode) {
                    BackgroundColorMode.SOLID -> "Gradient"
                    BackgroundColorMode.GRADIENT -> "Solid"
                },
                color = Color(0xFF015BFF),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .padding(end = 8.dp)
                    .clickable { onSwitchModeRemembered() }
            )
        }
    ) {
        if (backgroundMode == BackgroundColorMode.SOLID) {
            ColorPicker(
                onColorSelected = onSelectColor,
                colors = defaultColors
            )
        } else {
            ColorPicker(
                onGradientSelected = onGradientSelectedCallback,
                gradientColors = createGradientBrushes(),
                onListColorSelected = onListColorSelected
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        TransparentSlider(
            progress = gradientProgress,
            onProgressChanged = onGradientChanged,
            baseColor = currentColor,
            backgroundBrush = if (backgroundMode == BackgroundColorMode.GRADIENT) currentGradient.value else null
        )
    }
}

@Composable
fun ImportFromGalleryButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .dottedBorder(
                color = Color(0xFF015BFF).copy(alpha = 0.9f),
                strokeWidth = 4f,
                cornerRadius = 8.dp,
                dashLength = 8f,
                gapLength = 4f
            ),
        shape = RoundedCornerShape(8.dp),
        color = Color(0xFFE3F2FD),
    ) {
        Row(
            modifier = Modifier.padding(vertical = 24.dp, horizontal = 16.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(R.drawable.ic_gallery),
                contentDescription = null,
            )
            Text(
                text = "Import from Gallery",
                color = Color(0xFF015BFF),
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}

fun Modifier.dottedBorder(
    color: Color,
    strokeWidth: Float = 2f,
    cornerRadius: Dp = 16.dp,
    dashLength: Float = 10f,
    gapLength: Float = 5f
) = composed {
    val cornerRadiusPx = with(LocalDensity.current) { cornerRadius.toPx() }
    drawWithCache {
        val pathEffect = PathEffect.dashPathEffect(
            floatArrayOf(dashLength, gapLength), 0f
        )
        val path = Path().apply {
            addRoundRect(
                RoundRect(
                    rect = Rect(Offset.Zero, size),
                    cornerRadius = CornerRadius(cornerRadiusPx)
                )
            )
        }
        onDrawWithContent {
            drawContent()
            drawPath(
                path = path,
                color = color,
                style = Stroke(strokeWidth, pathEffect = pathEffect)
            )
        }
    }
}

@Composable
fun ColorPicker(
    modifier: Modifier = Modifier,
    onColorSelected: ((Color) -> Unit)? = null,
    onGradientSelected: ((Brush) -> Unit)? = null,
    onListColorSelected: ((List<Color>) -> Unit)? = null,
    colors: ImmutableList<Color> = defaultColors,
    gradientColors: ImmutableList<Brush> = persistentListOf()
) {
    var selectedColorIndex by remember { mutableIntStateOf(0) }
    val selectedItem by remember(selectedColorIndex, colors, gradientColors) {
        derivedStateOf {
            if (gradientColors.isNotEmpty() && selectedColorIndex < gradientColors.size) {
                gradientColors[selectedColorIndex]
            } else if (selectedColorIndex < colors.size) {
                colors[selectedColorIndex]
            } else {
                null
            }
        }
    }

    val haptic = LocalHapticFeedback.current

    LaunchedEffect(selectedItem) {
        if (selectedItem is Color && selectedColorIndex < colors.size) {
            onColorSelected?.invoke(selectedItem as Color)
        } else if (selectedItem is Brush && onGradientSelected != null) {
            onGradientSelected(selectedItem as Brush)
            if (selectedColorIndex < gradientPairs.size) {
                val (startColor, midColor, endColor) = gradientPairs[selectedColorIndex]
                onListColorSelected?.invoke(listOf(startColor, midColor, endColor))
            }
        }
    }

    val dragLogic = remember {
        object {
            var dragOffset = 0f

            fun onDragStart() {
                dragOffset = 0f
            }

            fun onDragEnd() {
                dragOffset = 0f
            }

            fun onDrag(change: PointerInputChange, dragAmount: Offset): Boolean {
                change.consume()
                dragOffset += dragAmount.x
                val threshold = 15f
                val maxIndex =
                    if (gradientColors.isNotEmpty()) gradientColors.size - 1 else colors.size - 1

                return when {
                    dragOffset > threshold && selectedColorIndex < maxIndex -> {
                        selectedColorIndex++
                        dragOffset = 0f
                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        true
                    }

                    dragOffset < -threshold && selectedColorIndex > 0 -> {
                        selectedColorIndex--
                        dragOffset = 0f
                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        true
                    }

                    else -> false
                }
            }
        }
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { dragLogic.onDragStart() },
                    onDragEnd = { dragLogic.onDragEnd() },
                    onDrag = { change, dragAmount -> dragLogic.onDrag(change, dragAmount) }
                )
            },
        verticalAlignment = Alignment.Bottom
    ) {
        val itemsList = if (gradientColors.isNotEmpty()) gradientColors else colors

        itemsList.forEachIndexed { index, item ->
            val isSelected = index == selectedColorIndex

            val height by animateDpAsState(
                targetValue = if (isSelected) 30.dp else 26.dp,
                label = "colorHeight",
            )
            val verticalPadding by animateDpAsState(
                targetValue = if (isSelected) 0.dp else 4.dp,
                label = "colorPadding",
            )

            Box(
                modifier = Modifier
                    .then(
                        if (isSelected) {
                            Modifier.border(
                                width = 1.dp,
                                color = Color.White,
                            )
                        } else Modifier
                    )
                    .weight(1f)
                    .height(height)
                    .padding(horizontal = 1.dp, vertical = verticalPadding)
                    .zIndex(if (isSelected) 1f else 0f)
                    .graphicsLayer {
                        shadowElevation = if (isSelected) 10f else 0f
                    }
                    .then(
                        if (item is Brush) {
                            Modifier.background(item)
                        } else {
                            Modifier.background(item as Color)
                        }
                    )
                    .clip(
                        when (index) {
                            0 -> RoundedCornerShape(topStart = 8.dp, bottomStart = 8.dp)
                            itemsList.size - 1 -> RoundedCornerShape(
                                topEnd = 8.dp,
                                bottomEnd = 8.dp
                            )

                            else -> RoundedCornerShape(0.dp)
                        }
                    )
                    .clickable {
                        if (selectedColorIndex != index) {
                            selectedColorIndex = index
                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        }
                    }
            )
        }
    }
}

@Preview
@Composable
fun CustomizeIconScreenPreview() {
    CustomizeIconScreen(
        state = CustomizeIconState.initialCustomizeIconState,
        onIntent = {},
        onNavigateBack = {}
    )
}