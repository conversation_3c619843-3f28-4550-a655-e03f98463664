package com.amobear.themepack.presentation.icons

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.amobear.themepack.presentation.main.navigation.destination.ThemeNavigationDestination

data object IconsDestination : ThemeNavigationDestination {
  override val route: String = "icons_route"
  override val destination: String = "icons_destination"
}

fun NavGraphBuilder.iconsGraph(
  onIconCustomizationSelected: () -> Unit,
  onNavigateToAppInstall: (String) -> Unit,
  ) =
  composable(route = IconsDestination.route) {
    IconsRoute(onIconCustomizationSelected = onIconCustomizationSelected, onNavigateToAppInstall = onNavigateToAppInstall)
  }