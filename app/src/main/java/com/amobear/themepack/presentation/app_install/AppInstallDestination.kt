package com.amobear.themepack.presentation.app_install

import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.amobear.themepack.presentation.main.navigation.destination.ThemeNavigationDestination
import java.net.URLDecoder
import java.net.URLEncoder
import kotlin.text.Charsets.UTF_8

object AppInstallDestination : ThemeNavigationDestination {
    override val route: String = "app_install_route"
    override val destination: String = "app_install_destination"

    const val IS_FONT_NEEDED_ARGUMENT = "isFontNeededArgument"
    const val ICON_SECTION_ARGUMENT = "iconSectionArgument"

    val routeWithArgument = "$route/{$IS_FONT_NEEDED_ARGUMENT}/{$ICON_SECTION_ARGUMENT}"

    fun createNavigationRoute(isFontNeeded: Boolean, iconSectionStr: String = "lmao"): String {
        val encodedIconSection = URLEncoder.encode(iconSectionStr, UTF_8.name())
        return "$route/$isFontNeeded/$encodedIconSection"
    }

    fun isFontNeededSavedState(savedStateHandle: SavedStateHandle): Boolean {
        return savedStateHandle.get<Boolean>(IS_FONT_NEEDED_ARGUMENT) ?: false
    }

    fun getIconSectionArgument(savedStateHandle: SavedStateHandle): String? {
        val encoded = savedStateHandle.get<String>(ICON_SECTION_ARGUMENT) ?: return null
        return if (encoded.isNotEmpty()) {
            URLDecoder.decode(encoded, UTF_8.name())
        } else null
    }
}

fun NavGraphBuilder.appInstallGraph(
    onNavigateBack: () -> Unit,
    onNavigateToResultScreen: () -> Unit,
) =
    composable(
        route = AppInstallDestination.routeWithArgument,
        arguments = listOf(
            navArgument(AppInstallDestination.IS_FONT_NEEDED_ARGUMENT) {
                type = androidx.navigation.NavType.BoolType
            }, navArgument(AppInstallDestination.ICON_SECTION_ARGUMENT) {
                type = androidx.navigation.NavType.StringType
            }
        )) {
        AppInstallRoute(
            onNavigateBack = onNavigateBack,
            onNavigateToResultScreen = onNavigateToResultScreen,
        )
    }