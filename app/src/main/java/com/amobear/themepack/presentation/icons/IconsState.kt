package com.amobear.themepack.presentation.icons

import androidx.compose.runtime.Stable
import com.amobear.themepack.domain.model.icon.CategoryItem
import com.amobear.themepack.domain.model.icon.IconItem
import com.amobear.themepack.domain.model.icon.IconSection
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Stable
data class IconsState(
    val categories: ImmutableList<CategoryItem> = persistentListOf(),
    val selectedCategory: String? = null,
    val iconSections: ImmutableList<IconSection> = persistentListOf(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val selectedIcon: IconItem? = null
)

sealed interface IconsEvent {
    data class NavigateToAppInstall(val appsStr: String) : IconsEvent
}
