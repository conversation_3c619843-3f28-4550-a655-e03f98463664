package com.amobear.themepack.presentation.home.screens.theme.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.ThemeSortOption
import com.amobear.themepack.data.network.onError
import com.amobear.themepack.data.network.onLoading
import com.amobear.themepack.data.network.onSuccess
import com.amobear.themepack.data.repository.ThemeRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Real implementation of ThemesViewModelInterface for the Themes screen
 */
@HiltViewModel
class ThemesViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val themeRepository: ThemeRepository
) : ViewModel(), ThemesViewModelInterface {

    private val _uiState = MutableStateFlow(ThemesUiState())
    override val uiState: StateFlow<ThemesUiState> = _uiState.asStateFlow()

    private val _effects = Channel<ThemesUiEffect>()
    override val effects: kotlinx.coroutines.flow.Flow<ThemesUiEffect> = _effects.receiveAsFlow()
    override fun handleEffect(effect: ThemesUiEffect) {
        // Handle the effect here
    }

    // Expose theme categories for the UI
    override val themeCategories: StateFlow<List<ThemeCategory>> =
        themeRepository.themeCategories.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    init {
        loadThemes()
    }

    /**
     * Load themes from repository with caching
     * @param forceRefresh Force refresh from network
     */
    override fun loadThemes(forceRefresh: Boolean) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            themeRepository.getThemeCategories(forceRefresh)
                .onSuccess { categories ->
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            themes = categories.flatMap { it.themes },
                            filteredThemes = categories.flatMap { it.themes },
                            isFromCache = !forceRefresh
                        )
                    }
                }
                .onError { exception ->
                    Timber.e(exception, "Error loading themes")
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            error = exception.message
                        )
                    }
                }
                .onLoading {
                    _uiState.update { it.copy(isLoading = true) }
                }
        }
    }

    /**
     * Refresh themes (force refresh from network)
     */
    override fun refreshThemes() {
        loadThemes(forceRefresh = true)
    }

    /**
     * Filter themes by category
     */
    override fun filterThemesByCategory(category: ThemeCategory?) {
        _uiState.update { currentState ->
            val filtered = if (category == null) {
                currentState.themes
            } else {
                currentState.themes.filter { it.categoryId == category.id }
            }
            currentState.copy(
                selectedCategory = category,
                filteredThemes = filtered
            )
        }
    }

    /**
     * Show no ads popup
     */
    override fun showNoAdsPopup() {
        _uiState.update { it.copy(showNoAdsPopup = true) }
    }

    /**
     * Dismiss no ads popup
     */
    override fun dismissNoAdsPopup() {
        _uiState.update { it.copy(showNoAdsPopup = false) }
    }

    /**
     * Show theme filter popup
     */
    override fun showFilterPopup() {
        _uiState.update { it.copy(showFilterPopup = true) }
    }

    /**
     * Dismiss theme filter popup
     */
    override fun dismissFilterPopup() {
        _uiState.update { it.copy(showFilterPopup = false) }
    }

    /**
     * Apply theme filter
     */
    override fun applyFilter(sortBy: ThemeSortOption, filterPremium: Boolean) {
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    sortOption = sortBy,
                    filterPremiumOnly = filterPremium,
                    showFilterPopup = false
                )
            }

            // Apply the filter
            applyFilters()
        }
    }

    /**
     * Apply current filters to themes
     */
    override fun applyFilters() {
        _uiState.update { currentState ->
            // First filter by category if selected
            var filtered = if (currentState.selectedCategory == null) {
                currentState.themes
            } else {
                currentState.themes.filter { it.categoryId == currentState.selectedCategory.id }
            }

            // Apply sorting (basic implementation for now)
            filtered = when (currentState.sortOption) {
                ThemeSortOption.NEWEST -> filtered.sortedByDescending { it.id } // Assuming newer themes have higher IDs
                ThemeSortOption.POPULAR -> filtered.sortedBy { it.title } // Sort by title as placeholder
                ThemeSortOption.RATING -> filtered.sortedBy { it.title } // Sort by title as placeholder
                ThemeSortOption.PRICE_LOW_TO_HIGH -> filtered.sortedBy { it.title } // Sort by title as placeholder
                ThemeSortOption.PRICE_HIGH_TO_LOW -> filtered.sortedByDescending { it.title } // Sort by title as placeholder
            }

            currentState.copy(filteredThemes = filtered)
        }
    }
}