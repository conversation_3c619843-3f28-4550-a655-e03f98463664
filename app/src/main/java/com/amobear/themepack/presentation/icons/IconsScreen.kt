package com.amobear.themepack.presentation.icons

import com.amobear.themepack.R
import android.widget.Toast
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Image
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.amobear.themepack.domain.model.icon.CategoryItem
import com.amobear.themepack.domain.model.icon.IconItem
import com.amobear.themepack.domain.model.icon.IconSection
import com.amobear.themepack.utils.CollectWithLifecycleEffect
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList

@Composable
fun IconsRoute(
    onIconCustomizationSelected: () -> Unit,
    onNavigateToAppInstall: (String) -> Unit,
    viewModel: IconsViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val context = LocalContext.current

    // Handle error messages
    LaunchedEffect(state.errorMessage) {
        state.errorMessage?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
        }
    }

    viewModel.eventFlow.CollectWithLifecycleEffect { event ->
        when (event) {
            is IconsEvent.NavigateToAppInstall -> {
                onNavigateToAppInstall(event.appsStr)
            }
        }
    }

    IconsScreen(
        state = state,
        onIntent = viewModel::processIntent,
        onFabClick = onIconCustomizationSelected
    )
}

@Composable
fun IconsScreen(
    state: IconsState,
    onIntent: (IconsIntent) -> Unit,
    modifier: Modifier = Modifier,
    onFabClick: () -> Unit = {},
) {
    val preloaderLottieComposition by rememberLottieComposition(
        LottieCompositionSpec.RawRes(
            R.raw.ic_custom_icons
        )
    )

    val preloaderProgress by animateLottieCompositionAsState(
        preloaderLottieComposition,
        iterations = LottieConstants.IterateForever,
        isPlaying = true
    )

    Scaffold(
        modifier = modifier.fillMaxSize(),
        floatingActionButton = {
            LottieAnimation(
                composition = preloaderLottieComposition,
                progress = { preloaderProgress },
                modifier = Modifier
                    .size(56.dp)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) { onFabClick() }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color(0xFFF6F2F2))
        ) {
            // Categories row
            if (state.categories.isNotEmpty()) {
                CategoriesRow(
                    categories = state.categories,
                    onCategorySelected = { categoryId ->
                        onIntent(IconsIntent.SelectCategory(categoryId))
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                )
            }

            // Content
            when {
                state.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFFFF80AB)
                        )
                    }
                }

                state.iconSections.isEmpty() && !state.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No icons available",
                            color = Color.Gray,
                            fontSize = 16.sp
                        )
                    }
                }

                else -> {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(2),
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 16.dp)
                    ) {
                        items(
                            items = state.iconSections,
                            key = { section -> section.title }
                        ) { section ->
                            IconSectionComponent(
                                section = section,
                                onIconSelected = { iconId ->
                                    onIntent(IconsIntent.SelectIcon(iconId, section.title))
                                },
                                onViewMore = if (section.hasMore) {
                                    { onIntent(IconsIntent.ViewMoreInCategory(section.title)) }
                                } else null
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun CategoriesRow(
    categories: ImmutableList<CategoryItem>,
    onCategorySelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(
            items = categories,
            key = { category -> category.id }
        ) { category ->
            CategoryChip(
                category = category,
                isSelected = category.isSelected,
                onClick = { onCategorySelected(category.id) }
            )
        }
    }
}

@Composable
fun CategoryChip(
    category: CategoryItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            Color(0xFFFF80AB)
        } else {
            Color.White
        },
        label = "CategoryChipBackgroundColor"
    )

    val contentColor by animateColorAsState(
        targetValue = if (isSelected) {
            Color.White
        } else {
            Color.Gray
        },
        label = "CategoryChipContentColor"
    )

    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        color = backgroundColor,
        onClick = onClick
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        ) {
            AsyncImage(
                model = category.iconUrl,
                contentDescription = "${category.name} icon",
                contentScale = ContentScale.Fit,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = category.name,
                color = contentColor,
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
            )
        }
    }
}

@Composable
fun IconSectionComponent(
    section: IconSection,
    onIconSelected: (String) -> Unit,
    onViewMore: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val displayIcons = section.icons.take(8)
        val itemCount = displayIcons.size
        val rows = (itemCount + 3) / 4
        val gridHeight = (rows * 48 + (rows - 1) * 4).dp // Include spacing

        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            userScrollEnabled = false,
            modifier = Modifier
                .height(gridHeight)
                .fillMaxWidth()
                .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                .padding(8.dp)
        ) {
            items(
                items = displayIcons,
                key = { icon -> icon.id }
            ) { icon ->
                IconItemComponent(
                    icon = icon,
                    onClick = { onIconSelected(icon.id) }
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = section.title,
                fontSize = 14.sp,
                color = Color.Black,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            if (section.hasMore && onViewMore != null) {
                Text(
                    text = "View More",
                    fontSize = 12.sp,
                    color = Color(0xFFFF80AB),
                    modifier = Modifier.clickable { onViewMore() }
                )
            }
        }
    }
}

@Composable
fun IconItemComponent(
    icon: IconItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .clip(RoundedCornerShape(12.dp))
            .background(Color(0xFFFFC107))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (!icon.imageUrl.isNullOrEmpty()) {
            AsyncImage(
                model = icon.imageUrl,
                contentDescription = "Icon ${icon.id}",
                contentScale = ContentScale.Crop,
                modifier = Modifier.fillMaxSize()
            )
        } else {
            Icon(
                imageVector = Icons.Default.Image,
                contentDescription = "Placeholder icon",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

// Preview
@Preview(showBackground = true)
@Composable
private fun IconsScreenPreview() {
    val categories = listOf(
        CategoryItem(id = "1", name = "Minimal", isSelected = true),
        CategoryItem(id = "2", name = "Nature", isSelected = false),
        CategoryItem(id = "3", name = "Urban", isSelected = false)
    ).toImmutableList()

    val icons = List(8) { index ->
        IconItem(
            id = "icon_$index",
            imageUrl = null,
            category = "minimal",
            isSelected = index == 2
        )
    }.toImmutableList()

    val iconSections = listOf(
        IconSection(title = "Social Media Pack", icons = icons, hasMore = true),
        IconSection(title = "Business Suite", icons = icons, hasMore = false),
        IconSection(title = "Essential Utilities", icons = icons, hasMore = true),
        IconSection(title = "Media Pack", icons = icons, hasMore = false)
    ).toImmutableList()

    val state = IconsState(
        categories = categories,
        iconSections = iconSections,
        isLoading = false,
        selectedCategory = "1"
    )

    IconsScreen(
        state = state,
        onIntent = {},
        onFabClick = {}
    )
}