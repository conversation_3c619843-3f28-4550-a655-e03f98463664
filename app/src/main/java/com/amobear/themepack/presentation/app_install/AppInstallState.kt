package com.amobear.themepack.presentation.app_install

import com.amobear.themepack.domain.model.app.AppItem
import com.amobear.themepack.presentation.customize_icon.CustomizeIconState
import com.amobear.themepack.presentation.customize_icon.FontOption
import com.amobear.themepack.utils.font.UnicodeTextTransformer
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

data class AppInstallState(
    val appItems: ImmutableList<AppItem> = persistentListOf(),
    val fonts: ImmutableList<FontOption> = persistentListOf(),
    val isLoading: Boolean = false,
    val showFonts: Boolean = false,
    val selectAllEnabled: Boolean = false,
    val showAppSelectionDialog: Boolean = false,
    val showEditAppNameDialog: Boolean = false,
    val showExitDialog: Boolean = false,
    var editingAppPackageName: String? = null,
) {
    companion object {
        val INITIAL = AppInstallState(
            fonts = UnicodeTextTransformer.MathStyle.entries.mapIndexed { index, font ->
                FontOption(
                    fontStyle = font,
                    isSelected = index == 0
                )
            }.toImmutableList()
        )
    }
}


sealed interface AppInstallEvent {
    data object Idle : AppInstallEvent
    data object NavigateToResultScreen : AppInstallEvent
}

/**
 * Singleton object to hold the state of the app installation process.
 * This might be stupid but it is used to share the state across different composables.
 * Don't blame me, blame the deadline.
 */
object AppInstallDataBundle {
    var customizedIconState: CustomizeIconState? = null

    fun changeFont(font: FontOption) {
        customizedIconState?.let { state ->
            customizedIconState = state.copy(
                fonts = state.fonts.map { it.copy(isSelected = it.fontStyle == font.fontStyle) }.toImmutableList()
            )
        }
    }
}
