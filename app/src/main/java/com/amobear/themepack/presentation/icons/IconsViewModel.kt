package com.amobear.themepack.presentation.icons

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.domain.model.icon.IconSection
import com.amobear.themepack.domain.usecase.GetCategoriesUseCase
import com.amobear.themepack.domain.usecase.GetIconSectionsUseCase
import com.amobear.themepack.presentation.base.EventChannel
import com.amobear.themepack.presentation.base.HasEventFlow
import com.squareup.moshi.Moshi
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class IconsViewModel @Inject constructor(
    private val getCategoriesUseCase: GetCategoriesUseCase,
    private val getIconSectionsUseCase: GetIconSectionsUseCase,
    private val eventChannel: EventChannel<IconsEvent>,
    private val moshi:  Moshi,
) : ViewModel(), HasEventFlow<IconsEvent> by eventChannel {

    private val _state = MutableStateFlow(IconsState())
    val state: StateFlow<IconsState> = _state.asStateFlow()

    init {
        processIntent(IconsIntent.LoadCategories)
    }
    fun processIntent(intent: IconsIntent) {
        when (intent) {
            is IconsIntent.LoadCategories -> {
                loadCategoriesAndIcons()
            }

            is IconsIntent.SelectCategory -> {
                selectCategory(intent.category)
            }

            is IconsIntent.SelectIcon -> {
                selectIcon(intent.iconId, intent.category)
            }

            is IconsIntent.ViewMoreInCategory -> {
                loadMoreIcons(intent.category)
            }
        }
    }

    private fun loadCategoriesAndIcons() {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }
            getCategoriesUseCase()
                .onSuccess { categories ->
                    Timber.tag("IconsViewModel").d("Loaded ${categories.size} categories")

                    val updatedCategories = if (categories.isNotEmpty()) {
                        categories.mapIndexed { index, category ->
                            category.copy(isSelected = index == 0)
                        }.toImmutableList()
                    } else {
                        persistentListOf()
                    }

                    val firstCategoryId = updatedCategories.firstOrNull()?.id
                    Timber.tag("IconsViewModel").d("First category ID: $firstCategoryId")

                    if (firstCategoryId != null) {
                        getIconSectionsUseCase(firstCategoryId)
                            .onSuccess { iconSections ->
                                Timber.tag("IconsViewModel").d("Loaded ${iconSections.size} icon sections for category $firstCategoryId")

                                _state.update {
                                    it.copy(
                                        categories = updatedCategories,
                                        selectedCategory = firstCategoryId,
                                        iconSections = iconSections,
                                        isLoading = false,
                                        errorMessage = null
                                    )
                                }
                            }
                            .onFailure { exception ->
                                Timber.tag("IconsViewModel").e(exception, "Failed to load icons for category $firstCategoryId")
                                _state.update {
                                    it.copy(
                                        categories = updatedCategories,
                                        selectedCategory = firstCategoryId,
                                        iconSections = persistentListOf(),
                                        isLoading = false,
                                        errorMessage = "Failed to load icons: ${exception.message}"
                                    )
                                }
                            }
                    } else {
                        _state.update {
                            it.copy(
                                categories = updatedCategories,
                                iconSections = persistentListOf(),
                                isLoading = false,
                                errorMessage = "No categories available"
                            )
                        }
                    }
                }
                .onFailure { exception ->
                    Timber.tag("IconsViewModel").e(exception, "Failed to load categories")
                    _state.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Failed to load categories: ${exception.message}"
                        )
                    }
                }
        }
    }

    private fun selectCategory(categoryId: String) {
        Timber.tag("IconsViewModel").d("Selecting category: $categoryId")
        val updatedCategories = _state.value.categories.map {
            it.copy(isSelected = it.id == categoryId)
        }.toImmutableList()

        _state.update {
            it.copy(
                categories = updatedCategories,
                selectedCategory = categoryId
            )
        }

        loadIconsForCategory(categoryId)
    }

    private fun loadIconsForCategory(categoryId: String) {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }

            getIconSectionsUseCase(categoryId)
                .onSuccess { iconSections ->
                    Timber.tag("IconsViewModel").d("Loaded ${iconSections.size} icon sections for category: $categoryId")

                    iconSections.forEach { section ->
                        Timber.tag("IconsViewModel").d("Section: ${section.title}, Icons: ${section.icons.size}")
                    }

                    _state.update {
                        it.copy(
                            iconSections = iconSections,
                            isLoading = false,
                            errorMessage = null
                        )
                    }
                }
                .onFailure { exception ->
                    Timber.tag("IconsViewModel").e(exception, "Failed to load icons for category $categoryId")
                    _state.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Failed to load icons: ${exception.message}"
                        )
                    }
                }
        }
    }

    private fun selectIcon(iconId: String, category: String) {
        val updatedSections = _state.value.iconSections.map { section ->
            val updatedIcons = section.icons.map { icon ->
                icon.copy(isSelected = icon.id == iconId && icon.category == category)
            }
            section.copy(icons = updatedIcons.toImmutableList())
        }.toImmutableList()

        val selectedSection = updatedSections.find { section ->
            section.icons.any { it.id == iconId && it.category == category }
        }
        busEvent(
            IconsEvent.NavigateToAppInstall(moshi.adapter(IconSection::class.java).toJson(selectedSection))
        )
        _state.update {
            it.copy(
                iconSections = updatedSections,
            )
        }
    }

    private fun loadMoreIcons(category: String) {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }

            val currentCategory = _state.value.selectedCategory
            if (currentCategory != null) {
                getIconSectionsUseCase(currentCategory)
                    .onSuccess { iconSections ->
                        _state.update {
                            it.copy(
                                iconSections = iconSections,
                                isLoading = false
                            )
                        }
                    }
                    .onFailure { exception ->
                        _state.update {
                            it.copy(
                                isLoading = false,
                                errorMessage = "Failed to load more icons: ${exception.message}"
                            )
                        }
                    }
            }
        }
    }

    private fun busEvent(event: IconsEvent) {
        viewModelScope.launch {
            eventChannel.send(event)
        }
    }
}