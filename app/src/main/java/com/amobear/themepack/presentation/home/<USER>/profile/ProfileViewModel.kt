package com.amobear.themepack.presentation.home.screens.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import com.amobear.themepack.data.model.IconPack
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.WidgetPack
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.repository.ThemeRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for Profile screen with unlock detection and offline cache support
 */
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val themeRepository: ThemeRepository,
    private val preferenceManager: SharePreferenceProvider
) : ViewModel() {
    
    private val _state = MutableStateFlow(ProfileScreenState())
    val state: StateFlow<ProfileScreenState> = _state.asStateFlow()
    
    init {
        loadUserCoins()
        loadUserProfile()
        loadUnlockedContent()
    }

    private fun loadUserCoins() {
        val coins = preferenceManager.getUserCoins()
        _state.value = _state.value.copy(coinBalance = coins)
    }
    
    /**
     * Load user profile and unlock statistics
     */
    private fun loadUserProfile() {
        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true)
        }
    }
    
    /**
     * Load unlocked content based on selected tab
     * Uses database-stored user library (not API data)
     */
    private fun loadUnlockedContent() {
        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true)

            try {
                when (_state.value.selectedTab) {
                    ProfileTab.THEME -> {
                        // Get user's unlocked themes from database
                        themeRepository.userUnlockedThemes.collect { themes ->
                            _state.value = _state.value.copy(
                                unlockedThemes = themes,
                                isLoading = false
                            )
                        }
                    }
                    ProfileTab.BACKGROUND -> {
                        // Get user's purchased wallpaper packs from database
                        themeRepository.userPurchasedWallpaperPacks.collect { packs ->
                            _state.value = _state.value.copy(
                                unlockedWallpaperPacks = packs,
                                isLoading = false
                            )
                        }
                    }
                    ProfileTab.ICON -> {
                        // Get user's purchased icon packs from database
                        themeRepository.userPurchasedIconPacks.collect { packs ->
                            _state.value = _state.value.copy(
                                unlockedIconPacks = packs,
                                isLoading = false
                            )
                        }
                    }
                    ProfileTab.ITEM -> {
                        // Get user's purchased widget packs from database
                        themeRepository.userPurchasedWidgetPacks.collect { packs ->
                            _state.value = _state.value.copy(
                                unlockedWidgetPacks = packs,
                                isLoading = false
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to load content: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Handle tab selection
     */
    fun onTabSelected(tab: ProfileTab) {
        _state.value = _state.value.copy(selectedTab = tab)
        // Load content for the newly selected tab
        loadUnlockedContent()
    }
    
    /**
     * Refresh unlocked content
     * Reloads user's library from database
     */
    fun refreshContent() {
        viewModelScope.launch {
            _state.value = _state.value.copy(isRefreshing = true)

            try {
                // Clean up orphaned unlocks first
                themeRepository.cleanupOrphanedUnlocks()

                // Reload current tab content
                loadUnlockedContent()

                _state.value = _state.value.copy(isRefreshing = false)
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    isRefreshing = false,
                    errorMessage = "Refresh failed: ${e.message}"
                )
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _state.value = _state.value.copy(errorMessage = null)
    }
    
    /**
     * Handle actual content purchase
     */
    fun purchaseContent(contentType: String, contentId: String, price: Int) {
        viewModelScope.launch {
            try {
                val currentCoins = preferenceManager.getUserCoins()
                
                if (currentCoins >= price) {
                    // Deduct coins for purchase
                    val success = preferenceManager.deductUserCoins(price)
                    
                    if (success) {
                        // Record the unlock using new architecture
                        val unlockSuccess = when (contentType) {
                            "THEME" -> {
                                // Purchase theme - this will update both image-based unlock and database
                                themeRepository.purchaseTheme(
                                    title = contentId, // Assuming contentId is the title
                                    description = "Purchased theme",
                                    previewImage = "", // Would need actual preview image URL
                                    categoryId = 0
                                )
                            }
                            "WIDGET_PACK" -> {
                                themeRepository.purchaseWidgetPack(
                                    title = contentId,
                                    previewImage = "", // Would need actual preview image URL
                                    weight = 0,
                                    coin = price,
                                    type = com.amobear.themepack.data.model.WidgetType.CLOCK,
                                    categoryId = 0
                                )
                            }
                            "ICON_PACK" -> {
                                themeRepository.purchaseIconPack(
                                    title = contentId,
                                    previewImage = "", // Would need actual preview image URL
                                    weight = 0,
                                    coin = price,
                                    categoryId = 0
                                )
                            }
                            else -> false
                        }

                        if (unlockSuccess) {
                            // Update coin balance and refresh content
                            val newCoins = preferenceManager.getUserCoins()
                            _state.value = _state.value.copy(coinBalance = newCoins)
                            refreshContent()
                        } else {
                            _state.value = _state.value.copy(
                                errorMessage = "Failed to save unlock status"
                            )
                        }
                    } else {
                        _state.value = _state.value.copy(
                            errorMessage = "Failed to process payment"
                        )
                    }
                } else {
                    _state.value = _state.value.copy(
                        errorMessage = "Insufficient coins. Need $price coins."
                    )
                }
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    errorMessage = "Purchase failed: ${e.message}"
                )
            }
        }
    }
}

/**
 * Enhanced Profile screen state with unlock tracking
 */
data class ProfileScreenState(
    val selectedTab: ProfileTab = ProfileTab.THEME,
    val coinBalance: Int = 0, // Will be loaded from SharedPreferences
    val unlockedThemes: List<Theme> = emptyList(),
    val unlockedWallpaperPacks: List<WallpaperPack> = emptyList(),
    val unlockedWidgetPacks: List<WidgetPack> = emptyList(),
    val unlockedIconPacks: List<IconPack> = emptyList(),
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val errorMessage: String? = null
)

/**
 * Unified content item for display in profile
 */
data class UnlockedContentItem(
    val id: String,
    val title: String,
    val description: String,
    val previewImage: String,
    val isDownloaded: Boolean,
    val localPath: String?
)

/**
 * Profile tabs enum
 */
enum class ProfileTab(val title: String) {
    THEME("Theme"),
    BACKGROUND("Background"), 
    ICON("Icon"),
    ITEM("Item")
} 