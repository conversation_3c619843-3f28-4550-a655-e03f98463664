package com.amobear.themepack.presentation.icon_setup_complete

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import com.amobear.themepack.R
import com.amobear.themepack.designsystem.ThemeAppTheme
import com.amobear.themepack.domain.IconApp
import com.google.android.gms.common.util.Base64Utils
import kotlinx.collections.immutable.persistentListOf

@Composable
fun SetupCompleteRoute(
    onNavigateBack: () -> Unit,
    onNavigateToIconSetup: () -> Unit = {},
    viewModel: SetupCompleteViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()

    SetupCompleteScreen(
        state = state,
        onIntent = { intent ->
            when (intent) {
                is SetupCompleteIntent.GoBack -> onNavigateBack()
                is SetupCompleteIntent.NavigateToIconSetup -> onNavigateToIconSetup()
                else -> viewModel.processIntent(intent)
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SetupCompleteScreen(
    state: SetupCompleteState,
    onIntent: (SetupCompleteIntent) -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = { },
                navigationIcon = {
                    IconButton(onClick = { onIntent(SetupCompleteIntent.GoBack) }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = Color.Black
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.Transparent,
                    titleContentColor = Color.Black
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(40.dp))
            Box(
                modifier = Modifier
                    .size(120.dp)
                    .clip(RoundedCornerShape(16.dp)),
                contentAlignment = Alignment.Center
            ) {
                AsyncImage(
                    model = state.mainIconFilePath,
                    contentDescription = "Main Icon",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .align(Alignment.BottomEnd)
                        .background(
                            color = Color(0xFF4CAF50),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Success message
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_aesthetics), // Party/celebration icon
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = Color(0xFFFF6B6B)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Setup done! Hope you enjoy it",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.Black
                )
            }

            Spacer(modifier = Modifier.height(80.dp))

            // Small native ads placeholder
            Text(
                text = "small native ads",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray,
                modifier = Modifier.padding(vertical = 40.dp)
            )

            Spacer(modifier = Modifier.height(40.dp))

            // Step 2 section
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.Start
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "Step 2",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black
                        )
                        Text(
                            text = "Set up your icons",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.Gray
                        )
                    }

                    TextButton(onClick = { onIntent(SetupCompleteIntent.SeeMore) }) {
                        Text(
                            text = "See more",
                            color = ThemeAppTheme.color.mainBlue,
                            style = MaterialTheme.typography.bodyMedium.copy(textDecoration = TextDecoration.Underline),
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        items(items = state.iconApps, key = { app ->
                            app.name
                        }) { iconApp ->
                            AppIconItem(
                                iconApp = iconApp,
                                onIconClick = { onIntent(SetupCompleteIntent.IconClicked(iconApp.id)) }
                            )
                        }
                    }

                    Button(
                        onClick = { onIntent(SetupCompleteIntent.NavigateToIconSetup) },
                        modifier = Modifier.size(56.dp),
                        shape = CircleShape,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ThemeAppTheme.color.mainBlue
                        ),
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            contentDescription = "Next",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

@Composable
fun AppIconItem(
    iconApp: IconApp,
    onIconClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .size(56.dp)
            .clickable { onIconClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(iconApp.backgroundColor)
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            if (iconApp.iconBase64 != null) {
                // Load icon from base64
                AsyncImage(
                    model = Base64Utils.decode(iconApp.iconBase64),
                    contentDescription = iconApp.name,
                    modifier = Modifier.size(32.dp)
                )
            } else {
                Icon(
                    painter = painterResource(id = iconApp.iconRes),
                    contentDescription = iconApp.name,
                    modifier = Modifier.size(32.dp),
                    tint = Color.Black
                )
            }
        }
    }
}

@Preview
@Composable
fun SetupCompleteScreenPreview() {
    SetupCompleteScreen(
        state = SetupCompleteState(
            iconApps = persistentListOf(
                IconApp(
                    id = "chrome",
                    name = "Chrome",
                    iconRes = R.drawable.ic_aesthetics,
                    backgroundColor = 0xFFFFB347,
                    iconBase64 = null
                ),
                IconApp(
                    id = "phone",
                    name = "Phone",
                    iconRes = R.drawable.ic_aesthetics,
                    backgroundColor = 0xFFFFB347,
                    iconBase64 = null
                ),
                IconApp(
                    id = "tiktok",
                    name = "TikTok",
                    iconRes = R.drawable.ic_aesthetics,
                    backgroundColor = 0xFFFFB347,
                    iconBase64 = null
                ),
                IconApp(
                    id = "plex",
                    name = "Plex",
                    iconRes = R.drawable.ic_aesthetics,
                    backgroundColor = 0xFFFFB347,
                    iconBase64 = null
                )
            )
        ),
        onIntent = {}
    )
}