package com.amobear.themepack.presentation.onboarding

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the onboarding screen
 */
@HiltViewModel
class OnboardingViewModel @Inject constructor(
    private val sharePreferenceProvider: SharePreferenceProvider
) : ViewModel() {

    /**
     * Complete the onboarding process
     */
    fun completeOnboarding() {
        viewModelScope.launch {
            sharePreferenceProvider.save<Boolean>(SharePreferenceProvider.FIRST_TIME_OPEN_APP, true)
        }
    }
}

class OnboardingViewModelMock : ViewModel() {
    fun completeOnboarding() {
    }
}