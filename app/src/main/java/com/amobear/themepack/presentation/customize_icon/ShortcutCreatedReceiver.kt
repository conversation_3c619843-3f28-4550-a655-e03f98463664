package com.amobear.themepack.presentation.customize_icon

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.widget.Toast
import com.amobear.themepack.presentation.app_install.ShortcutCreator.Companion.SHORTCUT_CREATION_SUCCESS

class ShortcutCreatedReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {

        if (intent.action == SHORTCUT_CREATION_SUCCESS) {
            val shortcutId = intent.getStringExtra(EXTRA_SHORTCUT_ID)

            if (shortcutId != null) {
                ShortcutCallbackRegistry.invokeCallback(shortcutId, true, null)
            }
        }
    }

    companion object {
        const val EXTRA_SHORTCUT_ID = "shortcut_id"
    }
}

object ShortcutCallbackRegistry {
    private val callbacks = mutableMapOf<String, (Boolean, String?) -> Unit>()

    fun registerCallback(shortcutId: String, callback: (<PERSON>olean, String?) -> Unit) {
        callbacks[shortcutId] = callback
    }

    fun invokeCallback(shortcutId: String, success: Boolean, message: String?) {
        callbacks[shortcutId]?.invoke(success, message)
        callbacks.remove(shortcutId)
    }
}