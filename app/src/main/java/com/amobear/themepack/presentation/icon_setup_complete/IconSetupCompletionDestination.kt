package com.amobear.themepack.presentation.icon_setup_complete

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.amobear.themepack.presentation.main.navigation.destination.ThemeNavigationDestination

data object IconSetupCompletionDestination : ThemeNavigationDestination {
    override val route: String = "icon_setup_completion_route"
    override val destination: String = "icon_setup_completion_destination"
}

fun NavGraphBuilder.iconSetupCompletionGraph(
    onNavigateBack: () -> Unit,
) =
    composable(route = IconSetupCompletionDestination.route) {
        SetupCompleteRoute(
            onNavigateBack = onNavigateBack,
        )
    }