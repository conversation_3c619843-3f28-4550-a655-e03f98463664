package com.amobear.themepack.presentation.app_install

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.amobear.themepack.R
import com.amobear.themepack.designsystem.ThemeAppTheme
import com.amobear.themepack.domain.model.app.AppItem
import com.amobear.themepack.domain.model.app.AppState
import com.amobear.themepack.presentation.app_install.component.AppItemCard
import com.amobear.themepack.presentation.app_install.dialog.ChangeIconNameDialog
import com.amobear.themepack.presentation.app_install.dialog.ConfirmExitDialog
import com.amobear.themepack.presentation.app_install.dialog.app_selection.AppSelectionBottomSheet
import com.amobear.themepack.presentation.customize_icon.FontSelector
import com.amobear.themepack.presentation.customize_icon.SectionWithTitle
import com.amobear.themepack.utils.CollectWithLifecycleEffect
import kotlinx.collections.immutable.persistentListOf

@Composable
fun AppInstallRoute(
    onNavigateBack: () -> Unit,
    onNavigateToResultScreen: () -> Unit,
    viewModel: AppInstallViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()

    viewModel.eventFlow.CollectWithLifecycleEffect { event ->
        when (event) {
            AppInstallEvent.Idle -> Unit
            AppInstallEvent.NavigateToResultScreen -> {
                onNavigateToResultScreen()
            }
        }
    }

    if (state.showExitDialog) {
        ConfirmExitDialog(
            onDismiss = { viewModel.processIntent(AppInstallIntent.DismissExitDialog) },
            onConfirm = onNavigateBack,
        )
    }


    AppSelectionBottomSheet(
        isVisible = state.showAppSelectionDialog,
        onDismiss = { viewModel.processIntent(AppInstallIntent.DismissAppSelection) },
        onAppSelected = { appInfo ->
            viewModel.processIntent(
                AppInstallIntent.AppSelected(
                    appInfo
                )
            )
        }
    )

    if (state.showEditAppNameDialog) {
        ChangeIconNameDialog(
            currentName = state.appItems.find { it.packageName == state.editingAppPackageName }?.name.orEmpty(),
            onDismiss = { viewModel.processIntent(AppInstallIntent.DismissEditAppName) },
            onConfirm = { newName ->
                viewModel.processIntent(
                    AppInstallIntent.ChangeAppItemName(
                        packageName = state.editingAppPackageName ?: "",
                        newName = newName
                    )
                )
            }
        )
    }


    AppInstallScreen(
        state = state,
        onIntent = viewModel::processIntent
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppInstallScreen(
    state: AppInstallState,
    onIntent: (AppInstallIntent) -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = { Text("Install") },
                navigationIcon = {
                    IconButton(onClick = { onIntent(AppInstallIntent.ExitPressed) }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = ThemeAppTheme.color.background,
                    titleContentColor = Color.Black
                )
            )
        },
        bottomBar = {
            Box(
                modifier = Modifier
                    .padding(16.dp)
                    .padding(top = 10.dp)
                    .fillMaxWidth()
                    .background(
                        color = Color.White
                    ),
            ) {
                UnlockAllIconsButton(
                    onClick = { onIntent(AppInstallIntent.UnlockAllIcons) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(color = ThemeAppTheme.color.background),
        ) {
            if (state.showFonts) {
                SectionWithTitle(title = "Front of icon") {
                    FontSelector(
                        fonts = state.fonts,
                        onSelectFont = { onIntent(AppInstallIntent.SelectFont(it)) },
                        backgroundColor = Color(0xFFEEE7E7)
                    )
                }
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Change icon",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                    TextButton(onClick = { onIntent(AppInstallIntent.SelectAll) }) {
                        Text(
                            text = "Select all",
                            color = ThemeAppTheme.color.mainBlue
                        )
                    }
                }
            }
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                items(items = state.appItems, key = { item ->
                    item.packageName
                }) { appItem ->
                    AppItemCard(
                        appItem = appItem,
                        onToggleSelection = { onIntent(AppInstallIntent.ToggleAppSelection(appItem.packageName)) },
                        onInstall = { onIntent(AppInstallIntent.InstallApp(appItem.packageName)) },
                        onWatchAd = { onIntent(AppInstallIntent.WatchAdForApp(appItem.packageName)) },
                        onAppSelecting = {
                            onIntent(AppInstallIntent.StartEditAppIcon(appItem.packageName))
                        },
                        onAppRename = {
                            onIntent(
                                AppInstallIntent.ShowEditAppNameDialog(
                                    packageName = appItem.packageName
                                )
                            )
                        },
                        onDestinationIconDelete = {
                            onIntent(AppInstallIntent.DeleteSelectedAppIcon(appItem.packageName))
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun UnlockAllIconsButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color.White
            ),
    ) {
        Button(
            onClick = onClick,
            modifier = Modifier
                .fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = ThemeAppTheme.color.mainBlue
            )
        ) {
            ConstraintLayout(
                modifier = Modifier.fillMaxWidth()
            ) {
                val (icon, textColumn) = createRefs()
                Icon(
                    painter = painterResource(id = R.drawable.ic_rewarded),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier
                        .size(24.dp)
                        .constrainAs(icon) {
                            start.linkTo(parent.start)
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                        }
                )

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .constrainAs(textColumn) {
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                            width = Dimension.fillToConstraints
                        }
                ) {
                    Text(
                        text = "Unlock all icon",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Watch an ads",
                        color = Color.White,
                        fontSize = 10.sp
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun AppInstallScreenPreview() {
    val context = LocalContext.current
    AppInstallScreen(
        state = AppInstallState.INITIAL.copy(
            showFonts = true,
            appItems = persistentListOf(
                AppItem(
                    name = "Lmao234234234324324324234234234234234234234n",
                    customIconUrl = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQr6WsCGy-o3brXcj2cmXGkHM_fE_p0gy4X8w&s",
                    destinationDrawable = null,
                    packageName = "com.example.sampleappnoicon",
                    isSelected = true,
                    appState = AppState.NOT_INSTALLED_AD_REQUIRED
                ),
                AppItem(
                    name = "Lmaon",
                    customIconUrl = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQr6WsCGy-o3brXcj2cmXGkHM_fE_p0gy4X8w&s",
                    destinationDrawable = null,
                    packageName = "com.example.sampleappnoicon",
                    isSelected = true,
                    appState = AppState.NOT_INSTALLED_AD_REQUIRED
                ),
                AppItem(
                    name = "Lmaon",
                    customIconUrl = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQr6WsCGy-o3brXcj2cmXGkHM_fE_p0gy4X8w&s",
                    destinationDrawable = null,
                    packageName = "com.example.sampleappnoicon",
                    isSelected = true,
                    appState = AppState.NOT_INSTALLED_AD_REQUIRED
                )
            )
        ),
        onIntent = {}
    )
}