package com.amobear.themepack.presentation.rewards

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.<PERSON>affold
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.amobear.themepack.R
import com.amobear.themepack.presentation.home.components.CoinIndicator
import com.amobear.themepack.presentation.rewards.components.CoinPackageCard
import com.amobear.themepack.presentation.rewards.components.DailyCheckInCard
import com.amobear.themepack.presentation.rewards.components.RewardTaskCard
import com.amobear.themepack.data.model.CoinPackage
import com.amobear.themepack.data.model.DailyReward
import com.amobear.themepack.data.model.RewardTask
import kotlinx.collections.immutable.persistentListOf

// 1. Route (Stateful)
@Composable
fun RewardsRoute(
    onNavigateBack: () -> Unit,
    viewModel: RewardsViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    RewardsScreen(
        state = state,
        onIntent = viewModel::processIntent,
        onNavigateBack = onNavigateBack
    )
}

// 2. Screen (Stateless)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsScreen(
    state: RewardsState,
    onIntent: (RewardsIntent) -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = Color(0xFFF6F2F2)

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.rewards_title),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_back), // Using existing icon, should be back arrow
                            contentDescription = "Back",
                            tint = Color.Black
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = backgroundColor
                )
            )
        },
        containerColor = backgroundColor
    ) { paddingValues ->
        RewardsContent(
            state = state,
            onIntent = onIntent,
            modifier = modifier.padding(paddingValues)
        )
    }
}

// 3. Content (Stateless, Dumb, Previewable)
@Composable
private fun RewardsContent(
    state: RewardsState,
    onIntent: (RewardsIntent) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Coin Balance Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.coin_balance),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )

                CoinIndicator(
                    coins = state.coinBalance,
                    onAddCoinsClick = {}
                )
            }
        }

        item {
            // Daily Check-in Card
            DailyCheckInCard(
                dailyRewards = state.dailyRewards,
                currentStreak = state.currentStreak,
                onCheckInClick = { onIntent(RewardsIntent.CheckIn) },
                showInfoDialog = state.showCheckInInfoDialog,
                onShowInfoDialog = { onIntent(RewardsIntent.ShowCheckInInfoDialog) },
                onDismissInfoDialog = { onIntent(RewardsIntent.DismissCheckInInfoDialog) }
            )
        }

        item {
            // Remind Me Tomorrow Toggle
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.remind_me_tomorrow),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black
                    )

                    Switch(
                        checked = state.remindMeTomorrow,
                        onCheckedChange = { onIntent(RewardsIntent.RemindMeTomorrowChanged(it)) },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = Color(0xFF4CAF50),
                            uncheckedThumbColor = Color.White,
                            uncheckedTrackColor = Color.Gray
                        )
                    )
                }
            }
        }

        items(state.rewardTasks) { task ->
            RewardTaskCard(
                task = task,
                onActionClick = { onIntent(RewardsIntent.TaskAction(task.id)) }
            )
        }

        item {
            // Coin Packages Section
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(vertical = 8.dp),
                modifier = Modifier.height(240.dp) // Fixed height for 2 rows
            ) {
                items(state.coinPackages) { coinPackage ->
                    CoinPackageCard(
                        coinPackage = coinPackage,
                        onPurchaseClick = { onIntent(RewardsIntent.PurchaseCoinPackage(coinPackage.id)) }
                    )
                }
            }
        }

        item {
            // Bottom spacing
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Preview(showBackground = true, heightDp = 1000)
@Composable
private fun RewardsContentPreview() {
    RewardsContent(
        state = RewardsState(
            coinBalance = 1250,
            dailyRewards = persistentListOf(
                DailyReward(1, 10, isCompleted = true),
                DailyReward(2, 20, isCompleted = true),
                DailyReward(3, 30, isToday = true),
                DailyReward(4, 60),
                DailyReward(5, 100),
                DailyReward(6, 200),
                DailyReward(7, 400)
            ),
            currentStreak = 2,
            rewardTasks = persistentListOf(
                RewardTask(
                    id = "daily_gift",
                    title = "Daily Gift",
                    description = "Get +50 coins per day",
                    coins = 50,
                    iconRes = R.drawable.ic_coin,
                    actionText = "Claim"
                ),
                RewardTask(
                    id = "watch_ads",
                    title = "Watch Ads (0/7)",
                    description = "Get +50 coins by watching ads",
                    coins = 50,
                    iconRes = R.drawable.ic_coin,
                    actionText = "Watch"
                )
            ),
            coinPackages = persistentListOf(
                CoinPackage("500", 500, "$1.99", R.drawable.ic_coin),
                CoinPackage("1500", 1500, "$2.99", R.drawable.ic_coin),
                CoinPackage("2500", 2500, "$3.99", R.drawable.ic_coin),
                CoinPackage("3500", 3500, "$4.99", R.drawable.ic_coin),
                CoinPackage("5000", 5000, "$5.99", R.drawable.ic_coin),
                CoinPackage("6000", 6000, "$6.99", R.drawable.ic_coin)
            ),
            remindMeTomorrow = true
        ),
        onIntent = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun RewardsScreenPreview() {
    RewardsScreen(
        state = RewardsState(
            coinBalance = 850,
            dailyRewards = persistentListOf(
                DailyReward(1, 10, isCompleted = true),
                DailyReward(2, 20, isCompleted = true),
                DailyReward(3, 30, isToday = true),
                DailyReward(4, 60),
                DailyReward(5, 100),
                DailyReward(6, 200),
                DailyReward(7, 400)
            ),
            currentStreak = 2,
            rewardTasks = persistentListOf(
                RewardTask(
                    id = "daily_gift",
                    title = "Daily Gift",
                    description = "Get +50 coins per day",
                    coins = 50,
                    iconRes = R.drawable.ic_coin,
                    actionText = "Claim"
                )
            ),
            coinPackages = persistentListOf(
                CoinPackage("500", 500, "$1.99", R.drawable.ic_coin),
                CoinPackage("1500", 1500, "$2.99", R.drawable.ic_coin),
                CoinPackage("2500", 2500, "$3.99", R.drawable.ic_coin),
                CoinPackage("3500", 3500, "$4.99", R.drawable.ic_coin),
                CoinPackage("5000", 5000, "$5.99", R.drawable.ic_coin),
                CoinPackage("6000", 6000, "$6.99", R.drawable.ic_coin)
            ),
            remindMeTomorrow = false
        ),
        onIntent = {},
        onNavigateBack = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun RewardsContentLoadingPreview() {
    RewardsContent(
        state = RewardsState(isLoading = true),
        onIntent = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun RewardsContentEmptyPreview() {
    RewardsContent(
        state = RewardsState(
            coinBalance = 0,
            dailyRewards = persistentListOf(),
            rewardTasks = persistentListOf(),
            coinPackages = persistentListOf()
        ),
        onIntent = {}
    )
}

@Preview(showBackground = true, widthDp = 400, heightDp = 300)
@Composable
private fun CoinPackagesGridPreview() {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(16.dp),
        modifier = Modifier.height(240.dp)
    ) {
        items(
            listOf(
                CoinPackage("500", 500, "$1.99", R.drawable.ic_coin),
                CoinPackage("1500", 1500, "$2.99", R.drawable.ic_coin),
                CoinPackage("2500", 2500, "$3.99", R.drawable.ic_coin),
                CoinPackage("3500", 3500, "$4.99", R.drawable.ic_coin),
                CoinPackage("5000", 5000, "$5.99", R.drawable.ic_coin),
                CoinPackage("6000", 6000, "$6.99", R.drawable.ic_coin)
            )
        ) { coinPackage ->
            CoinPackageCard(
                coinPackage = coinPackage,
                onPurchaseClick = {}
            )
        }
    }
}