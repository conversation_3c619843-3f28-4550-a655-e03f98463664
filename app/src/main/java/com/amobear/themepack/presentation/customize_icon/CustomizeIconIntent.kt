package com.amobear.themepack.presentation.customize_icon

import android.graphics.Bitmap
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

sealed class CustomizeIconIntent {
    data object LoadDefaultSettings : CustomizeIconIntent()
    data class SelectFont(val fontType: String) : CustomizeIconIntent()
    data class SelectSymbolColor(val color: Color) : CustomizeIconIntent()
    data class SetSymbolColorGradient(val progress: Float) : CustomizeIconIntent()
    data class SelectBackgroundColor(val color: Color) : CustomizeIconIntent()
    data class SetBackgroundColorGradient(val progress: Float) : CustomizeIconIntent()
    data class SelectBackgroundGradient(val brush: Brush) : CustomizeIconIntent()
    data class SelectGradientColorList(val colors: List<Color>) : CustomizeIconIntent()
    data class SelectBackgroundImage(val imageBitmap: Bitmap) : CustomizeIconIntent()
    data object SwitchBackgroundColorMode : CustomizeIconIntent()
    data object OnChooseImage : CustomizeIconIntent()
    data object SaveAndExit : CustomizeIconIntent()
}

