package com.amobear.themepack.presentation.app_install

import com.amobear.themepack.domain.model.AppInfo

sealed class AppInstallIntent {
    data object ExitPressed : AppInstallIntent()
    data object SelectAll : AppInstallIntent()
    data class ToggleAppSelection(val packageName: String) : AppInstallIntent()
    data class InstallApp(val packageName: String) : AppInstallIntent()
    data class WatchAdForApp(val packageName: String) : AppInstallIntent()
    data object UnlockAllIcons : AppInstallIntent()
    data class StartEditAppIcon(val packageName: String) : AppInstallIntent()
    data class ChangeAppItemName(val packageName: String, val newName: String) : AppInstallIntent()
    data class ShowAppSelectionDialog(val packageName: String) : AppInstallIntent()
    data class ShowEditAppNameDialog(val packageName: String) : AppInstallIntent()
    data class SelectFont(val fontType: String) : AppInstallIntent()
    data object ShowExitDialog : AppInstallIntent()
    data class DeleteSelectedAppIcon(val packageName: String) : AppInstallIntent()
    data object DismissAppSelection : AppInstallIntent()
    data object DismissEditAppName : AppInstallIntent()
    data object DismissExitDialog : AppInstallIntent()
    data class AppSelected(val appInfo: AppInfo) : AppInstallIntent()
}