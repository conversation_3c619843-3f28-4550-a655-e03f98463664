package com.amobear.themepack.presentation.wallpaper_generator

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import androidx.compose.ui.platform.LocalContext
import com.amobear.themepack.R
import com.amobear.themepack.data.model.Wallpaper
import com.amobear.themepack.presentation.home.components.CoinIndicator
import com.amobear.themepack.presentation.main.navigation.AppLocalNavController
import com.amobear.themepack.presentation.rewards.RewardsDestination
import com.amobear.themepack.presentation.wallpaper_generator.components.AddTagsBottomSheet
import kotlinx.coroutines.launch

// Dummy data classes and sample data
@Stable
data class StyleItem(val id: String, val name: String, val imageRes: Int)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WallpaperGeneratorScreen(
    onBackClick: () -> Unit,
    onNavigateToAiWallpaper: () -> Unit = {},
    viewModel: WallpaperGeneratorViewModelInterface = hiltViewModel<WallpaperGeneratorViewmodel>()
) {
    val generationCost = 150 // Example

    val addTagsSheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    val navController = AppLocalNavController.current
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        topBar = {
            GeneratorTopAppBar(
                currentCoins = uiState.userCoins,
                onAddCoinsClick = {
                    navController?.navigate(RewardsDestination.route) {
                        launchSingleTop = true
                    }
                },
                onBackClick = onBackClick
            )
        },
        bottomBar = {
            GenerationCtaBar(
                cost = generationCost,
                onGenerateClick = { /* TODO */ }
            )
        },
        containerColor = Color(0xFFF8F8F8) // Light gray background
    ) { paddingValues ->
        WallpaperGeneratorContent(
            uiState,
            onHandleEffect = viewModel::handleEffect,
            onNavigateToAiWallpaper,
            modifier = Modifier.padding(paddingValues)
        )
    }

    if (uiState.isShowTagsSheet) {
        AddTagsBottomSheet(
            sheetState = addTagsSheetState,
            initialSelectedTags = uiState.selectedTags,
            onDismissRequest = { viewModel.handleEffect(WallpaperGeneratorUiEffect.DismissAddTagsSheet) },
            onConfirm = { confirmedTags ->
                viewModel.handleEffect(WallpaperGeneratorUiEffect.UpdateTags(confirmedTags))
                scope.launch {
                    addTagsSheetState.hide()
                }.invokeOnCompletion {
                    if (!addTagsSheetState.isVisible) {
                        viewModel.handleEffect(WallpaperGeneratorUiEffect.DismissAddTagsSheet)
                    }
                }
            }
        )
    }
}

@Composable
private fun WallpaperGeneratorContent(
    uiState: WallpaperGeneratorUiState,
    onHandleEffect: (WallpaperGeneratorUiEffect) -> Unit,
    onNavigateToAiWallpaper: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        Spacer(modifier = Modifier.height(16.dp))

        // AI Wallpaper Generator Test Button
        Button(
            onClick = onNavigateToAiWallpaper,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFFF76CE)
            )
        ) {
            Text(
                text = "🤖 Test AI Wallpaper Generator (Amobear API)",
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
        IdeaInputCard(
            tagsCount = uiState.selectedTags.size,
            ideaText = uiState.prompt,
            onTextChange = { { onHandleEffect(WallpaperGeneratorUiEffect.UpdatePrompt(it)) } },
            onClearClick = { { onHandleEffect(WallpaperGeneratorUiEffect.UpdatePrompt("")) } },
            onTagsAreaClick = { { onHandleEffect(WallpaperGeneratorUiEffect.ShowAddTagsSheet) } },
        )
        Spacer(modifier = Modifier.height(24.dp))
        StyleSelector(
            selectedStyle = uiState.selectedStyle,
            onStyleSelected = { { onHandleEffect(WallpaperGeneratorUiEffect.UpdateStyle(it)) } }
        )
        Spacer(modifier = Modifier.height(24.dp))
        BaseImageDisplay(wallpapers = uiState.downloadedWallpapers)
        Spacer(modifier = Modifier.height(16.dp)) // Spacer for content above CTA
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun GeneratorTopAppBar(
    currentCoins: Int,
    onAddCoinsClick: () -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Text(
                "Generate with AI",
                fontWeight = FontWeight.SemiBold,
                fontSize = 18.sp,
                color = Color.Black
            )
        },
        navigationIcon = {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.Black
                )
            }
        },
        actions = {
            CoinIndicator(
                coins = currentCoins,
                onAddCoinsClick = onAddCoinsClick,
                modifier = Modifier
                    .padding(end = 16.dp)
                    .wrapContentSize()
            )
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.White,
            scrolledContainerColor = Color.White
        ),
        modifier = modifier
    )
}

@Composable
private fun IdeaInputCard(
    tagsCount: Int,
    ideaText: String,
    onTextChange: (String) -> Unit,
    onClearClick: () -> Unit,
    onTagsAreaClick: () -> Unit
) {
    Column {
        Text(
            "Type your idea",
            fontWeight = FontWeight.SemiBold,
            fontSize = 14.sp,
            color = Color(0xFF212121),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(150.dp)
                .clip(RoundedCornerShape(16.dp))
                .border(1.dp, Color(0xFF015BFF), RoundedCornerShape(16.dp))
                .background(Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                ) {
                    BasicTextField(
                        value = ideaText,
                        onValueChange = onTextChange,
                        modifier = Modifier.fillMaxSize(),
                        textStyle = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF212121),
                        ),
                        cursorBrush = SolidColor(Color(0xFF015BFF)),
                        decorationBox = { innerTextField ->
                            if (ideaText.isEmpty()) {
                                Text(
                                    "Beautiful girl in Asia",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF212121),
                                )
                            }
                            innerTextField()
                        }
                    )
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.clickable(onClick = onTagsAreaClick)
                    ) {
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier
                                .size(28.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(Color(0xFFE9E9E9))
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_tag),
                                contentDescription = "Tags",
                                modifier = Modifier.size(16.dp),
                                tint = Color(0xFF212121)
                            )
                        }
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = "$tagsCount Tags",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black
                        )
                    }

                    if (ideaText.isNotEmpty()) {
                        IconButton(
                            onClick = onClearClick,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                Icons.Default.Close,
                                contentDescription = "Clear text",
                                tint = Color(0xFF474747)
                            )
                        }
                    } else {
                        Spacer(modifier = Modifier.size(24.dp))
                    }
                }
            }
        }
    }
}

@Composable
private fun StyleSelector(
    selectedStyle: String,
    onStyleSelected: (String) -> Unit
) {
    val styles =
        remember {
            listOf(
                StyleItem("ghibli", "Ghibli", R.drawable.ghibli),
                StyleItem("anime", "Anime", R.drawable.anime),
                StyleItem("tattoo", "Tattoo", R.drawable.tattoo),
                StyleItem("realistic", "Realistic", R.drawable.realistic),
                StyleItem("artistic", "Artistic", R.drawable.artistic)
            )

        }
    Column {
        Text(
            "Choose style",
            fontWeight = FontWeight.Medium,
            fontSize = 16.sp,
            color = Color.Black,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(styles) { style ->
                StyleCard(
                    style = style,
                    isSelected = style.id == selectedStyle,
                    onClick = { onStyleSelected(style.id) }
                )
            }
        }
    }
}

@Composable
private fun StyleCard(
    style: StyleItem,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable(onClick = onClick)
            .width(70.dp)
    ) {
        Image(
            painter = painterResource(id = style.imageRes),
            contentDescription = style.name,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .size(70.dp, 90.dp)
                .clip(RoundedCornerShape(8.dp))
                .border(
                    width = 2.dp,
                    color = if (isSelected) Color(0xFF007AFF) else Color.Transparent,
                    shape = RoundedCornerShape(8.dp)
                )
        )
        Spacer(modifier = Modifier.height(6.dp))
        Text(
            text = style.name,
            fontSize = 13.sp,
            color = if (isSelected) Color(0xFF007AFF) else Color.Gray,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun BaseImageDisplay(wallpapers: List<com.amobear.themepack.data.repository.DownloadedWallpaper>) {
    Column {
        Text(
            "Your images",
            fontWeight = FontWeight.Medium,
            fontSize = 16.sp,
            color = Color.Black,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        if (wallpapers.isEmpty()) {
            Text(
                "No wallpapers unlocked yet. Download themes to see them here!",
                color = Color.Gray,
                modifier = Modifier.align(Alignment.CenterHorizontally),
                textAlign = TextAlign.Center
            )
        } else {
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.heightIn(max = 200.dp)
            ) {
                items(wallpapers) { downloadedWallpaper ->
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(downloadedWallpaper.url)
                            .crossfade(true)
                            .build(),
                        contentDescription = "Downloaded wallpaper",
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .aspectRatio(1f)
                            .clip(RoundedCornerShape(8.dp)),
                        placeholder = painterResource(id = R.drawable.placeholder_theme),
                        error = painterResource(id = R.drawable.placeholder_theme)
                    )
                }
            }
        }
    }
}

@Composable
private fun GenerationCtaBar(
    cost: Int,
    onGenerateClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(vertical = 16.dp, horizontal = 16.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp)
        ) {
            Text("Price to generate:", fontSize = 14.sp, color = Color.Gray)
            Spacer(modifier = Modifier.width(6.dp))
            Icon(
                painter = painterResource(id = R.drawable.ic_coin),
                contentDescription = "Coin",
                tint = Color.Unspecified,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = cost.toString(),
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
        }
        Button(
            onClick = onGenerateClick,
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            shape = RoundedCornerShape(12.dp),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF007AFF))
        ) {
            Text("Use $cost coins", fontSize = 16.sp, fontWeight = FontWeight.SemiBold)
        }
        Spacer(modifier = Modifier.height(10.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp)
                .background(Color(0xFF212232))
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(
                    modifier = Modifier
                        .background(Color.Yellow)
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text("ADS", color = Color.Black, fontWeight = FontWeight.Bold, fontSize = 12.sp)
                }
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Ad Zone: In order to continue developing the app, we need to show Ad in this position",
                    color = Color.White,
                    fontSize = 10.sp,
                )
            }
        }
    }
}

@Preview(showBackground = true, device = "spec:width=360dp,height=780dp,dpi=420")
@Composable
fun WallpaperGeneratorScreenPreview() {
    MaterialTheme {
        WallpaperGeneratorScreen(onBackClick = {})
    }
} 