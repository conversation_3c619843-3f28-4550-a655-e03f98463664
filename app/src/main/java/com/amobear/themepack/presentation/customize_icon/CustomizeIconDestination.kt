package com.amobear.themepack.presentation.customize_icon

import android.net.Uri
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.amobear.themepack.presentation.main.navigation.destination.ThemeNavigationDestination

data object CustomizeIconDestination : ThemeNavigationDestination {
    override val route: String = "customize_icon_route"
    override val destination: String = "customize_icon_destination"
}

fun NavGraphBuilder.customizeIconGraph(
    onNavigateBack: () -> Unit,
    onNavigateToAppIconChanging: () -> Unit,
) =
    composable(route = CustomizeIconDestination.route) {
        CustomizeIconRoute(onNavigateBack = onNavigateBack, onNavigateToAppIconChanging = onNavigateToAppIconChanging)
    }