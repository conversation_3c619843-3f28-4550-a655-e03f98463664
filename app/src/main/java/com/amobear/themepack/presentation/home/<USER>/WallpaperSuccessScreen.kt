package com.amobear.themepack.presentation.home.screens

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintSet
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.amobear.themepack.R
import com.amobear.themepack.data.model.Icon
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.Widget
import com.amobear.themepack.data.model.WidgetSize

/**
 * Screen shown after successfully setting up a wallpaper
 */
@Composable
fun WallpaperSuccessScreen(
    theme: Theme?,
    icons: List<Icon> = emptyList(),
    widgets: List<Widget> = emptyList(),
    onBack: () -> Unit,
    onSetupIcons: () -> Unit,
    onSetupWidgets: () -> Unit
) {
    val context = LocalContext.current

    WallpaperSuccessScreenContent(
        onBack,
        theme,
        context,
        onSetupIcons,
        icons,
        onSetupWidgets,
        widgets
    )
}

@Composable
private fun WallpaperSuccessScreenContent(
    onBack: () -> Unit,
    theme: Theme?,
    context: Context,
    onSetupIcons: () -> Unit,
    icons: List<Icon>,
    onSetupWidgets: () -> Unit,
    widgets: List<Widget>
) {
    val constraints = ConstraintSet {
        val statusBar = createRefFor("statusBar")
        val backButton = createRefFor("backButton")
        val phonePreview = createRefFor("phonePreview")
        val successCheckmark = createRefFor("successCheckmark")
        val successMessage = createRefFor("successMessage")
        val nativeAdsSection = createRefFor("nativeAdsSection")
        val step2Section = createRefFor("step2Section")
        val step2SeeMore = createRefFor("step2SeeMore")
        val iconGrid = createRefFor("iconGrid")
        val step3Section = createRefFor("step3Section")
        val step3SeeMore = createRefFor("step3SeeMore")
        val widgetContainers = createRefFor("widgetContainers")
        val step3SeeMoreButton = createRefFor("step3SeeMoreButton")
        val backgroundPreview = createRefFor("backgroundPreview")
        val adsBanner = createRefFor("adsBanner")

        constrain(statusBar) {
            top.linkTo(parent.top)
            start.linkTo(parent.start)
            end.linkTo(parent.end)
        }

        constrain(backButton) {
            top.linkTo(statusBar.bottom, margin = 24.dp)
            start.linkTo(parent.start, margin = 16.dp)
        }

        constrain(phonePreview) {
            top.linkTo(backButton.top)
            start.linkTo(parent.start)
            end.linkTo(parent.end)
        }

        constrain(successCheckmark) {
            top.linkTo(phonePreview.bottom, margin = (-16).dp)
            start.linkTo(phonePreview.end, margin = (-16).dp)
        }

        constrain(successMessage) {
            top.linkTo(successCheckmark.bottom, margin = 22.dp)
            start.linkTo(parent.start)
            end.linkTo(parent.end)
        }

        constrain(nativeAdsSection) {
            top.linkTo(successMessage.bottom, margin = 42.dp)
            start.linkTo(parent.start)
            end.linkTo(parent.end)
        }

        constrain(step2Section) {
            top.linkTo(nativeAdsSection.bottom, margin = 16.dp)
            start.linkTo(parent.start, margin = 16.dp)
        }

        constrain(step2SeeMore) {
            bottom.linkTo(step2Section.bottom, margin = (-1).dp)
            end.linkTo(parent.end, margin = 32.dp)
        }

        constrain(iconGrid) {
            top.linkTo(step2Section.bottom, margin = 16.dp)
            start.linkTo(parent.start, margin = 16.dp)
            end.linkTo(parent.end)
        }

        constrain(step3Section) {
            top.linkTo(iconGrid.bottom, margin = 16.dp)
            start.linkTo(parent.start, margin = 16.dp)
        }

        constrain(step3SeeMore) {
            bottom.linkTo(step3Section.bottom, margin = (-1).dp)
            end.linkTo(parent.end, margin = 32.dp)
        }

        constrain(widgetContainers) {
            top.linkTo(step3Section.bottom, margin = 28.dp)
            start.linkTo(parent.start, margin = 16.dp)
            end.linkTo(parent.end)
        }

        constrain(step3SeeMoreButton) {
            top.linkTo(widgetContainers.top)
            bottom.linkTo(widgetContainers.bottom)
            end.linkTo(parent.end, margin = 16.dp)
        }

        constrain(backgroundPreview) {
            top.linkTo(iconGrid.bottom, margin = 7.dp)
            start.linkTo(parent.start, margin = 20.dp)
            end.linkTo(parent.end, margin = 20.dp)
        }

        constrain(adsBanner) {
            start.linkTo(parent.start)
            end.linkTo(parent.end)
            bottom.linkTo(parent.bottom)
        }
    }

    ConstraintLayout(
        constraintSet = constraints,
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF6F2F2))
    ) {
        // Back button
        IconButton(
            onClick = onBack,
            modifier = Modifier
                .size(24.dp)
                .layoutId("backButton")
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                contentDescription = "Back",
                tint = Color.Black,
                modifier = Modifier.size(24.dp)
            )
        }

        // Phone preview with wallpaper
        Card(
            modifier = Modifier
                .size(width = 100.dp, height = 188.dp)
                .shadow(
                    elevation = 12.dp,
                    shape = RoundedCornerShape(16.dp),
                    ambientColor = Color(0x0C000000),
                    spotColor = Color(0x0C000000)
                )
                .layoutId("phonePreview"),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.Transparent)
        ) {
            if (theme != null) {
                // Show actual theme preview
                AsyncImage(
                    model = ImageRequest.Builder(context)
                        .data(theme.previewImage)
                        .crossfade(true)
                        .build(),
                    contentDescription = theme.title,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize(),
                    placeholder = painterResource(id = R.drawable.placeholder_theme),
                    error = painterResource(id = R.drawable.placeholder_theme)
                )
            } else {
                // Fallback to placeholder
                AsyncImage(
                    model = "https://placehold.co/100x188",
                    contentDescription = "Wallpaper preview",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }

        // Success checkmark icon
        Icon(
            modifier = Modifier
                .size(32.dp)
                .layoutId("successCheckmark"),
            painter = painterResource(id = R.drawable.ic_check_circle),
            contentDescription = "Success",
            tint = Color(0xFF4CAF50)
        )

        // Success message with emoji
        Row(
            modifier = Modifier.layoutId("successMessage"),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.emoji_party_popper_1),
                contentDescription = "Party emoji",
                modifier = Modifier.size(20.dp),
                tint = Color(0xFFFF6B6B)
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = "Setup done! Hope you enjoy it",
                color = Color(0xFF212121),
                fontSize = (12).sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
        }

        // Small native ads section
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(110.dp)
                .background(Color(0xFFEBEBEB))
                .layoutId("nativeAdsSection"),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "small native ads",
                color = Color(0xFF9F9F9F),
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal
            )
        }

        // Step 2 Section
        Column(
            modifier = Modifier.layoutId("step2Section")
        ) {
            Text(
                text = "Step 2",
                color = Color(0xFF212121),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "Set up your icons",
                color = Color(0xFF515151),
                fontSize = (12).sp,
                fontWeight = FontWeight.SemiBold
            )
        }

        // See more button for Step 2
        Text(
            text = "See more",
            color = Color(0xFF015BFF),
            fontSize = (12).sp,
            fontWeight = FontWeight.SemiBold,
            textDecoration = TextDecoration.Underline,
            textAlign = TextAlign.Right,
            modifier = Modifier
                .clickable { onSetupIcons() }
                .layoutId("step2SeeMore")
        )

        // Icon grid for Step 2
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .layoutId("iconGrid")
        ) {
            LazyRow(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                for (index in 0 until minOf(4, icons.size)) {
                    item {
                        Box(
                            modifier = Modifier
                                .size(56.dp)
                                .background(
                                    Color(0xFFEEE7E7),
                                    RoundedCornerShape(16.dp)
                                )
                                .clickable { onSetupIcons() }
                        ) {
                            // Show actual icon
                            AsyncImage(
                                model = ImageRequest.Builder(context)
                                    .data(icons[index].imageUrl)
                                    .crossfade(true)
                                    .build(),
                                contentDescription = icons[index].name,
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .fillMaxSize()
                                    .clip(RoundedCornerShape(16.dp)),
                                placeholder = painterResource(id = R.drawable.placeholder_theme),
                                error = painterResource(id = R.drawable.placeholder_theme)
                            )
                        }
                    }
                }
            }
            Box(
                modifier = Modifier
                    .size(56.dp)
                    .background(Color(0xFF015BFF), CircleShape)
                    .clickable { onSetupWidgets() }
                    .layoutId("step3SeeMoreButton"),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_arrow_right_stroke),
                    contentDescription = "See more widgets",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }

        // Step 3 Section
        Column(
            modifier = Modifier.layoutId("step3Section")
        ) {
            Text(
                text = "Step 3",
                color = Color(0xFF212121),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "Set your widgets",
                color = Color(0xFF515151),
                fontSize = (12).sp,
                fontWeight = FontWeight.SemiBold
            )
        }

        // See more button for Step 3
        Text(
            text = "See more",
            color = Color(0xFF015BFF),
            fontSize = (12).sp,
            fontWeight = FontWeight.SemiBold,
            textDecoration = TextDecoration.Underline,
            textAlign = TextAlign.Right,
            modifier = Modifier
                .clickable { onSetupWidgets() }
                .layoutId("step3SeeMore")
        )

        // Widget containers for Step 3
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(end = 88.dp)
                .layoutId("widgetContainers"),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Small widget container
            Box(
                modifier = Modifier
                    .size(70.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color(0xFFEEE7E7))
                    .clickable { onSetupWidgets() }
            ) {
                if (widgets.isNotEmpty()) {
                    // Show actual widget preview
                    AsyncImage(
                        model = ImageRequest.Builder(context)
                            .data(widgets[0].previewImage)
                            .crossfade(true)
                            .build(),
                        contentDescription = widgets[0].title,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize(),
                        placeholder = painterResource(id = R.drawable.placeholder_theme),
                        error = painterResource(id = R.drawable.placeholder_theme)
                    )
                } else {
                    // Widget icon placeholder for empty state
                    Icon(
                        painter = painterResource(id = R.drawable.ic_arrow_right_stroke),
                        contentDescription = "Widget placeholder",
                        tint = Color.Gray,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .size(24.dp)
                    )
                }
            }

            // Large widget container
            Box(
                modifier = Modifier
                    .height(height = 70.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color(0xFFEEE7E7))
                    .clickable { onSetupWidgets() }
            ) {
                if (widgets.size > 1) {
                    // Show actual widget preview
                    AsyncImage(
                        model = ImageRequest.Builder(context)
                            .data(widgets[1].previewImage)
                            .crossfade(true)
                            .build(),
                        contentDescription = widgets[1].title,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize(),
                        placeholder = painterResource(id = R.drawable.placeholder_theme),
                        error = painterResource(id = R.drawable.placeholder_theme)
                    )
                } else {
                    // Widget preview placeholder for empty state
                    Icon(
                        painter = painterResource(id = R.drawable.ic_arrow_right_stroke),
                        contentDescription = "Widget placeholder",
                        tint = Color.Gray,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .size(24.dp)
                    )
                }
            }
        }

        // See more circle button for Step 3
        Box(
            modifier = Modifier
                .size(56.dp)
                .background(Color(0xFF015BFF), CircleShape)
                .clickable { onSetupWidgets() }
                .layoutId("step3SeeMoreButton"),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_right_stroke),
                contentDescription = "See more widgets",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }

        // Bottom ad banner
        AdsBanner(
            modifier = Modifier.layoutId("adsBanner")
        )
    }
}

@Composable
private fun AdsBanner(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(50.dp)
            .background(Color(0xFF212232))
    ) {
        Row(
            modifier = Modifier
                .padding(start = 28.dp, top = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // ADS label
            Box(
                modifier = Modifier
                    .size(34.dp)
                    .background(Color(0xFFFDD301)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "ADS",
                    color = Color(0xFF212232),
                    fontSize = (12).sp,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.width(8.dp))

            Column {
                Text(
                    text = "Ad Zone",
                    color = Color.White,
                    fontSize = (12).sp,
                    fontWeight = FontWeight.SemiBold
                )

                Text(
                    text = "In order to continue developing the app,\nwe need show Ad in this position",
                    color = Color.White,
                    fontSize = (10).sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = (12).sp
                )
            }
        }
    }
}

@Composable
@androidx.compose.ui.tooling.preview.Preview(
    name = "Wallpaper Success Screen",
    showBackground = true,
    backgroundColor = 0xFFF6F2F2,
    device = "spec:width=360dp,height=800dp"
)
private fun WallpaperSuccessScreenPreview() {
    // Sample theme data using correct model
    val sampleTheme = Theme(
        id = 1,
        title = "Nature Theme",
        description = "Beautiful nature wallpaper",
        previewImage = "https://placehold.co/100x188/4CAF50/FFFFFF?text=Nature",
        categoryId = 1,
        wallpaperPacks = emptyList(),
        widgetPacks = emptyList(),
        iconPacks = emptyList(),
    )

    // Sample icons data using correct model
    val sampleIcons = listOf(
        Icon(
            id = 1,
            appId = "com.camera.app",
            name = "Camera",
            imageUrl = "https://placehold.co/56x56/2196F3/FFFFFF?text=📷",
            iconPackId = 1,
        ),
        Icon(
            id = 2,
            appId = "com.gallery.app",
            name = "Gallery",
            imageUrl = "https://placehold.co/56x56/FF9800/FFFFFF?text=🖼️",
            iconPackId = 1,
        ),
        Icon(
            id = 3,
            appId = "com.music.app",
            name = "Music",
            imageUrl = "https://placehold.co/56x56/9C27B0/FFFFFF?text=🎵",
            iconPackId = 1,
        )
    )

    // Sample widgets data using correct model
    val sampleWidgets = listOf(
        Widget(
            id = 1,
            title = "Weather Widget",
            previewImage = "https://placehold.co/70x70/03A9F4/FFFFFF?text=☀️",
            width = 2,
            height = 2,
            size = WidgetSize.SMALL,
            packId = 1,
            isInstalled = false,
            configuration = emptyMap()
        ),
        Widget(
            id = 2,
            title = "Clock Widget",
            previewImage = "https://placehold.co/170x70/607D8B/FFFFFF?text=🕐",
            width = 4,
            height = 2,
            size = WidgetSize.MEDIUM,
            packId = 1,
            isInstalled = false,
            configuration = emptyMap()
        )
    )

    WallpaperSuccessScreen(
        theme = sampleTheme,
        icons = sampleIcons,
        widgets = sampleWidgets,
        onBack = { },
        onSetupIcons = { },
        onSetupWidgets = { }
    )
}
