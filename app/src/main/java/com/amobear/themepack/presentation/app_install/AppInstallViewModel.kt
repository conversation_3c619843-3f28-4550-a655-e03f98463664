package com.amobear.themepack.presentation.app_install

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.core.AppCoroutineDispatchers
import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import com.amobear.themepack.domain.model.AppInfo
import com.amobear.themepack.domain.model.app.AppItem
import com.amobear.themepack.domain.model.app.AppState
import com.amobear.themepack.domain.model.icon.IconSection
import com.amobear.themepack.extension.hasCreatedShortcutFor
import com.amobear.themepack.extension.loadImageAsBitmap
import com.amobear.themepack.presentation.base.EventChannel
import com.amobear.themepack.presentation.base.HasEventFlow
import com.amobear.themepack.presentation.customize_icon.CustomizeIconState
import com.squareup.moshi.Moshi
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class AppInstallViewModel @Inject constructor(
    private val sharePreferenceProvider: SharePreferenceProvider,
    private val eventChannel: EventChannel<AppInstallEvent>,
    private val moshi: Moshi,
    @ApplicationContext private val context: Context,
    private val savedStateHandle: SavedStateHandle,
    private val dispatchers: AppCoroutineDispatchers,
) : ViewModel(), HasEventFlow<AppInstallEvent> by eventChannel {

    private val packageManager = context.packageManager

    private val _state = MutableStateFlow(
        AppInstallState.INITIAL.copy(
            showFonts = AppInstallDestination.isFontNeededSavedState(savedStateHandle),
            appItems = loadInitialAppItems()
        )
    )
    val state: StateFlow<AppInstallState> = _state.asStateFlow()

    init {
        AppInstallDataBundle.customizedIconState = CustomizeIconState(
            fonts = _state.value.fonts,
        )
        loadAppItems()
    }

    private fun loadInitialAppItems(): ImmutableList<AppItem> {
        return runCatching {
            AppInstallDestination.getIconSectionArgument(savedStateHandle)?.let { json ->
                moshi.adapter(IconSection::class.java).fromJson(json)?.icons
                    ?.map { icon ->
                        AppItem(
                            name = icon.name,
                            customIconUrl = icon.imageUrl.orEmpty(),
                            destinationDrawable = null,
                            packageName = icon.appPackageName
                        )
                    }
                    ?.toImmutableList()
            }
        }.getOrNull() ?: persistentListOf()
    }

    private fun loadAppItems() {
        viewModelScope.launch(dispatchers.default) {
            val currentAppItems = _state.value.appItems

            val sourceAppItems = if (currentAppItems.isEmpty()) {
                getSampleAppItems()
            } else {
                currentAppItems
            }

            val processedAppItems = sourceAppItems.map { appItem ->
                async {
                    val packageName = appItem.packageName

                    val drawableDeferred = async(dispatchers.io) {
                        runCatching {
                            packageManager.getApplicationIcon(packageName)
                        }.getOrNull()
                    }

                    val appShortcutAdsWatchedDeferred = async(dispatchers.io) {
                        sharePreferenceProvider.get<Boolean>(
                            key = SharePreferenceProvider.KEY_AD_WATCHED_FOR_APP + packageName
                        ) == true
                    }

                    val appShortcutInstalledDeferred = async(dispatchers.main) {
                        context.hasCreatedShortcutFor(packageName)
                    }

                    val destinationDrawable = drawableDeferred.await()
                    val appShortcutAdsWatched = appShortcutAdsWatchedDeferred.await()
                    val appShortcutInstalled = appShortcutInstalledDeferred.await()

                    appItem.copy(
                        destinationDrawable = destinationDrawable,
                        appState = when {
                            appShortcutInstalled -> AppState.INSTALLED
                            appShortcutAdsWatched -> AppState.NOT_INSTALLED_DIRECT
                            else -> AppState.NOT_INSTALLED_AD_REQUIRED
                        }
                    )
                }
            }.awaitAll().toImmutableList()

            withContext(dispatchers.main) {
                _state.update { it.copy(appItems = processedAppItems, isLoading = false) }
            }
        }
    }

    private suspend fun selectIconState(imageUrl: String) {
        withContext(dispatchers.io) {
            val bitmap = imageUrl.loadImageAsBitmap(context)
            if (AppInstallDataBundle.customizedIconState == null) {
                AppInstallDataBundle.customizedIconState = CustomizeIconState(
                    fonts = _state.value.fonts,
                    backgroundImage = bitmap,
                )
            } else {
                AppInstallDataBundle.customizedIconState =
                    AppInstallDataBundle.customizedIconState?.copy(
                        backgroundImage = bitmap
                    )
            }
        }
    }

    private fun updateFontState() {
        viewModelScope.launch {
            if (AppInstallDataBundle.customizedIconState == null) {
                AppInstallDataBundle.customizedIconState = CustomizeIconState(
                    fonts = _state.value.fonts,
                )
            } else {
                AppInstallDataBundle.customizedIconState =
                    AppInstallDataBundle.customizedIconState?.copy(
                        fonts = _state.value.fonts,
                    )
            }
        }
    }

    private fun selectFont(fontName: String) {
        val updatedFonts = _state.value.fonts.map { font ->
            font.copy(isSelected = font.fontStyle.name == fontName)
        }.toImmutableList()
        AppInstallDataBundle.changeFont(updatedFonts.first { it.isSelected })
        _state.update {
            it.copy(
                fonts = updatedFonts,
            )
        }
        if (_state.value.showFonts)
            updateFontState()
    }

    fun processIntent(intent: AppInstallIntent) {
        when (intent) {
            is AppInstallIntent.ExitPressed -> {
                processIntent(AppInstallIntent.ShowExitDialog)
            }

            is AppInstallIntent.SelectAll -> {
                val areAllSelected = _state.value.appItems.all { it.isSelected }
                _state.update { currentState ->
                    currentState.copy(
                        appItems = currentState.appItems.map { it.copy(isSelected = !areAllSelected) }
                            .toImmutableList(),
                        selectAllEnabled = !areAllSelected
                    )
                }
            }

            is AppInstallIntent.ToggleAppSelection -> {
                _state.update { currentState ->
                    currentState.copy(
                        appItems = currentState.appItems.map {
                            if (it.packageName == intent.packageName) it.copy(isSelected = !it.isSelected)
                            else it
                        }.toImmutableList()
                    )
                }
            }

            is AppInstallIntent.WatchAdForApp -> {
                _state.update { currentState ->
                    currentState.copy(
                        appItems = currentState.appItems.map {
                            if (it.packageName == intent.packageName && it.appState == AppState.NOT_INSTALLED_AD_REQUIRED) {
                                it.copy(appState = AppState.NOT_INSTALLED_DIRECT)
                            } else {
                                it
                            }
                        }.toImmutableList()
                    )
                }
            }

            is AppInstallIntent.UnlockAllIcons -> {
                _state.update { currentState ->
                    currentState.copy(
                        appItems = currentState.appItems.map {
                            if (it.appState == AppState.NOT_INSTALLED_AD_REQUIRED && it.isSelected) {
                                it.copy(appState = AppState.NOT_INSTALLED_DIRECT)
                            } else {
                                it
                            }
                        }.toImmutableList()
                    )
                }
            }

            is AppInstallIntent.StartEditAppIcon -> {
                _state.update { currentState ->
                    currentState.copy(
                        showAppSelectionDialog = true,
                        editingAppPackageName = intent.packageName
                    )
                }
            }

            is AppInstallIntent.InstallApp -> {
                handleShortcutInstallationState(intent.packageName)
            }

            is AppInstallIntent.AppSelected -> {
                handleAppSelection(intent.appInfo)
                _state.update { currentState ->
                    currentState.copy(
                        showAppSelectionDialog = false,
                        editingAppPackageName = null
                    )
                }
            }

            is AppInstallIntent.DismissAppSelection -> {
                _state.update {
                    it.copy(
                        showAppSelectionDialog = false,
                        editingAppPackageName = null
                    )
                }
            }

            is AppInstallIntent.ChangeAppItemName -> {
                changeAppItemName(intent.packageName, intent.newName)
            }

            AppInstallIntent.DismissEditAppName -> {
                _state.update { currentState ->
                    currentState.copy(
                        showEditAppNameDialog = false,
                        editingAppPackageName = null
                    )
                }
            }

            is AppInstallIntent.ShowAppSelectionDialog -> {
                _state.update { currentState ->
                    currentState.copy(
                        showAppSelectionDialog = true,
                        editingAppPackageName = intent.packageName
                    )
                }
            }

            is AppInstallIntent.ShowEditAppNameDialog -> {
                _state.update { currentState ->
                    currentState.copy(
                        showEditAppNameDialog = true,
                        editingAppPackageName = intent.packageName
                    )
                }
            }

            is AppInstallIntent.DeleteSelectedAppIcon -> handleDeleteAppIcon(intent.packageName)
            is AppInstallIntent.DismissExitDialog -> {
                _state.update { it.copy(showExitDialog = false) }
            }

            is AppInstallIntent.ShowExitDialog -> {
                _state.update { it.copy(showExitDialog = true) }
            }

            is AppInstallIntent.SelectFont -> {
                selectFont(intent.fontType)
            }
        }
    }

    private fun handleDeleteAppIcon(packageName: String) {
        viewModelScope.launch {
            val currentAppItems = _state.value.appItems
            val updatedAppItems = currentAppItems.map { app ->
                if (app.packageName == packageName) {
                    app.copy(destinationDrawable = null)
                } else {
                    app
                }
            }.toImmutableList()
            _state.update { it.copy(appItems = updatedAppItems) }
        }
    }

    private fun handleAppSelection(appInfo: AppInfo) {
        val currentEditingAppPackageName = _state.value.editingAppPackageName
        val currentApp =
            _state.value.appItems.find { it.packageName == currentEditingAppPackageName }

        if (currentApp != null) {
            val updatedAppItem = currentApp.copy(
                name = appInfo.appName,
                packageName = appInfo.packageName,
                destinationDrawable = appInfo.icon,
            )

            val updatedAppItems = _state.value.appItems.toMutableList()
            val index =
                updatedAppItems.indexOfFirst { it.packageName == currentEditingAppPackageName }
            if (index >= 0) {
                updatedAppItems[index] = updatedAppItem
            }

            _state.update { it.copy(appItems = updatedAppItems.toImmutableList()) }
        }

    }

    private fun changeAppItemName(appPackageName: String, newName: String) {
        viewModelScope.launch {
            val currentAppItems = _state.value.appItems
            val appItemIndex = currentAppItems.indexOfFirst { it.packageName == appPackageName }

            if (appItemIndex >= 0) {
                val appItem = currentAppItems[appItemIndex]
                val updatedAppItems = currentAppItems.toMutableList()
                updatedAppItems[appItemIndex] = appItem.copy(name = newName)

                _state.update {
                    it.copy(
                        appItems = updatedAppItems.toImmutableList(),
                        showEditAppNameDialog = false
                    )
                }
            }
        }
    }

    private fun handleShortcutInstallationState(appPackageName: String) {
        viewModelScope.launch {
            val currentAppItems = _state.value.appItems
            val appItemIndex = currentAppItems.indexOfFirst { it.packageName == appPackageName }
            if (appItemIndex < 0) return@launch

            val appItem = currentAppItems[appItemIndex]

            when (appItem.appState) {
                AppState.NOT_INSTALLED_DIRECT -> {
                    if (_state.value.showFonts)
                        selectIconState(appItem.customIconUrl)
                    AppInstallDataBundle.customizedIconState?.createHomeScreenShortcut(
                        context = context,
                        packageName = appItem.packageName,
                        customLabel = appItem.name
                    ) { isSuccess, content ->
                        if (isSuccess) {
                            val latestAppItems = _state.value.appItems
                            val latestAppItemIndex =
                                latestAppItems.indexOfFirst { it.packageName == appPackageName }
                            if (latestAppItemIndex >= 0) {
                                val latestAppItem = latestAppItems[latestAppItemIndex]
                                val updatedAppItem = latestAppItem.copy(
                                    appState = AppState.INSTALLED,
                                    destinationDrawable = runCatching {
                                        packageManager.getApplicationIcon(appItem.packageName)
                                    }.getOrNull()
                                )
                                val updatedAppItems = latestAppItems.toMutableList().apply {
                                    this[latestAppItemIndex] = updatedAppItem
                                }
                                _state.update { it.copy(appItems = updatedAppItems.toImmutableList()) }
                                busEvent(AppInstallEvent.NavigateToResultScreen)
                            }
                        } else {
                            // Handle error case if needed
                        }
                    }
                }

                AppState.NOT_INSTALLED_AD_REQUIRED -> {
                    sharePreferenceProvider.save<Boolean>(
                        key = SharePreferenceProvider.KEY_AD_WATCHED_FOR_APP + appItem.packageName,
                        value = true
                    )
                    val latestAppItems = _state.value.appItems
                    val latestAppItemIndex =
                        latestAppItems.indexOfFirst { it.packageName == appPackageName }
                    if (latestAppItemIndex >= 0) {
                        val latestAppItem = latestAppItems[latestAppItemIndex]
                        val updatedAppItems = latestAppItems.toMutableList().apply {
                            this[latestAppItemIndex] =
                                latestAppItem.copy(appState = AppState.NOT_INSTALLED_DIRECT)
                        }
                        _state.update { it.copy(appItems = updatedAppItems.toImmutableList()) }
                    }
                }

                AppState.INSTALLED -> {
                    // No action needed
                }
            }
        }
    }

    private fun busEvent(event: AppInstallEvent) {
        viewModelScope.launch {
            eventChannel.send(event)
        }
    }

    private fun getSampleAppItems(): List<AppItem> {
        return listOf(
            AppItem(
                name = "LinkedIn",
                customIconUrl = "",
                destinationDrawable = null,
                packageName = "com.linkedin.android",
                isSelected = true,
                appState = AppState.NOT_INSTALLED_DIRECT
            ),
            AppItem(
                name = "Instagram",
                customIconUrl = "",
                destinationDrawable = null,
                packageName = "com.instagram.android",
                isSelected = false,
                appState = AppState.NOT_INSTALLED_DIRECT
            ),
            AppItem(
                name = "YouTube",
                customIconUrl = "",
                destinationDrawable = null,
                packageName = "com.google.android.youtube",
                isSelected = true,
                appState = AppState.NOT_INSTALLED_DIRECT
            ),
            AppItem(
                name = "Gmail",
                customIconUrl = "",
                destinationDrawable = null,
                packageName = "com.google.android.gm",
                isSelected = true,
                appState = AppState.NOT_INSTALLED_AD_REQUIRED
            ),
            AppItem(
                name = "Settings",
                customIconUrl = "",
                destinationDrawable = null,
                packageName = "com.android.settings",
                isSelected = true,
                appState = AppState.NOT_INSTALLED_AD_REQUIRED
            ),
            AppItem(
                name = "Phone",
                customIconUrl = "",
                destinationDrawable = null,
                packageName = "com.android.dialer",
                isSelected = true,
                appState = AppState.NOT_INSTALLED_AD_REQUIRED
            )
        )
    }

}