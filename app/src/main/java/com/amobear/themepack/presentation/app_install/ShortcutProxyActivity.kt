package com.amobear.themepack.presentation.app_install

import android.content.ComponentName
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ShortcutProxyActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val packageName = intent.getStringExtra("packageName")
        val mainActivity = intent.getStringExtra("mainActivity")

        if (packageName != null && mainActivity != null) {
            try {
                val launchIntent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory(Intent.CATEGORY_LAUNCHER)
                    component = ComponentName(packageName, mainActivity)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                }
                startActivity(launchIntent)
            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this, "Failed to launch app", Toast.LENGTH_SHORT).show()
            }
        }

        finish()
    }
}