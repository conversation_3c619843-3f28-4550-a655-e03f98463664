package com.amobear.themepack.presentation.icon_setup_complete

import com.amobear.themepack.domain.IconApp
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

data class SetupCompleteState(
    val mainIconFilePath : String? = null,
    val iconApps: ImmutableList<IconApp> = persistentListOf(),
    val isLoading: Boolean = false,
    val error: String? = null
)