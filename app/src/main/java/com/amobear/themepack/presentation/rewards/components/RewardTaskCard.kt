package com.amobear.themepack.presentation.rewards.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import com.amobear.themepack.R
import com.amobear.themepack.data.model.RewardTask

@Composable
fun RewardTaskCard(
    task: RewardTask,
    onActionClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Task icon
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFF5F5F5)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = task.iconRes),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Task info
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp)
            ) {
                Text(
                    text = task.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                Text(
                    text = task.description,
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            // Action button
            Button(
                onClick = onActionClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = when (task.actionText) {
                        "Claim" -> Color(0xFF4CAF50)
                        "Watch" -> Color(0xFF2196F3)
                        "Share" -> Color(0xFF2196F3)
                        else -> Color(0xFF2196F3)
                    }
                ),
                shape = RoundedCornerShape(8.dp),
                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
            ) {
                Text(
                    text = task.actionText,
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun RewardTaskCardPreview() {
    RewardTaskCard(
        task = RewardTask(
            id = "daily_gift",
            title = "Daily Gift",
            description = "Get +50 coins per day",
            coins = 50,
            iconRes = R.drawable.ic_coin,
            actionText = "Claim"
        ),
        onActionClick = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun RewardTaskCardWatchPreview() {
    RewardTaskCard(
        task = RewardTask(
            id = "watch_ads",
            title = "Watch Ads (0/7)",
            description = "Get +50 coins by watching ads",
            coins = 50,
            iconRes = R.drawable.ic_coin,
            actionText = "Watch"
        ),
        onActionClick = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun RewardTaskCardSharePreview() {
    RewardTaskCard(
        task = RewardTask(
            id = "share_friends",
            title = "Share with friends",
            description = "Get +50 coins by sharing the app with your friends",
            coins = 50,
            iconRes = R.drawable.ic_coin,
            actionText = "Share"
        ),
        onActionClick = {}
    )
}