package com.amobear.themepack.presentation.customize_icon

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Shader
import android.graphics.drawable.Icon
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.core.graphics.createBitmap
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import androidx.core.graphics.scale
import com.amobear.themepack.utils.font.UnicodeTextTransformer

@HiltViewModel
class CustomizeIconViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _state = MutableStateFlow(CustomizeIconState.initialCustomizeIconState)
    val state: StateFlow<CustomizeIconState> = _state.asStateFlow()

    init {
        processIntent(CustomizeIconIntent.LoadDefaultSettings)
    }

    fun processIntent(intent: CustomizeIconIntent) {
        when (intent) {
            is CustomizeIconIntent.LoadDefaultSettings -> {
                loadDefaultSettings()
            }

            is CustomizeIconIntent.SelectFont -> {
                selectFont(intent.fontType)
            }

            is CustomizeIconIntent.SelectSymbolColor -> {
                selectSymbolColor(intent.color)
            }

            is CustomizeIconIntent.SelectBackgroundColor -> {
                selectBackgroundColor(intent.color)
            }

            is CustomizeIconIntent.OnChooseImage -> {}
            is CustomizeIconIntent.SaveAndExit -> {
                saveSettings()
            }

            is CustomizeIconIntent.SetSymbolColorGradient -> {
                updateColorWithGradient(
                    progress = intent.progress,
                    isSymbol = true
                )
            }

            is CustomizeIconIntent.SetBackgroundColorGradient -> {
                updateColorWithGradient(
                    progress = intent.progress,
                    isSymbol = false
                )
            }

            is CustomizeIconIntent.SelectBackgroundGradient -> {
                updateBackgroundGradient(intent.brush)
            }

            is CustomizeIconIntent.SelectBackgroundImage -> {
                updateBackgroundImage(intent.imageBitmap)
            }

            CustomizeIconIntent.SwitchBackgroundColorMode -> {
                switchBackgroundColorMode()
            }

            is CustomizeIconIntent.SelectGradientColorList -> {
                updateColorListOfGradient(intent)
            }
        }
    }

    private fun updateColorListOfGradient(intent: CustomizeIconIntent.SelectGradientColorList) {
        val colors = intent.colors
        _state.update {
            it.copy(gradientColors = colors)
        }
    }

    private fun updateBackgroundGradient(brush: Brush) {
        _state.update { currentState ->
            currentState.copy(
                backgroundGradient = brush,
                backgroundImage = null
            )
        }
    }

    private fun updateBackgroundImage(imageBitmap: Bitmap) {
        _state.update { currentState ->
            currentState.copy(
                backgroundImage = imageBitmap
            )
        }
    }

    private fun switchBackgroundColorMode() {
        _state.update { currentState ->
            val newMode = when (currentState.backgroundColorMode) {
                BackgroundColorMode.SOLID -> BackgroundColorMode.GRADIENT
                BackgroundColorMode.GRADIENT -> BackgroundColorMode.SOLID
            }
            currentState.copy(backgroundColorMode = newMode)
        }
    }


    private fun updateColorWithGradient(progress: Float, isSymbol: Boolean) {
        _state.update {
            val currentColor = if (isSymbol) it.selectedSymbolColor else it.selectedBackgroundColor

            val newColor = Color(
                red = currentColor.red,
                green = currentColor.green,
                blue = currentColor.blue,
                alpha = progress
            )

            if (isSymbol) {
                it.copy(
                    symbolColorAlpha = progress,
                    selectedSymbolColor = newColor
                )
            } else {
                it.copy(
                    backgroundColorAlpha = progress,
                    selectedBackgroundColor = newColor,
                    backgroundImage = null
                )
            }
        }
    }

    private fun loadDefaultSettings() {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }
            val fonts = UnicodeTextTransformer.MathStyle.entries.mapIndexed { idx, font ->
                FontOption(
                    fontStyle = font,
                    isSelected = idx == 0
                )
            }.toImmutableList()
            _state.update {
                it.copy(
                    fonts = fonts,
                    isLoading = false
                )
            }
        }
    }

    private fun selectFont(fontName: String) {
        val updatedFonts = _state.value.fonts.map { font ->
            font.copy(isSelected = font.fontStyle.name == fontName)
        }.toImmutableList()
        _state.update {
            it.copy(
                fonts = updatedFonts,
            )
        }
    }

    private fun selectSymbolColor(color: Color) {
        _state.update { colorState ->
            colorState.copy(
                selectedSymbolColor = color
            )
        }
    }

    private fun selectBackgroundColor(color: Color) {
        _state.update { colorState ->
            colorState.copy(
                selectedBackgroundColor = color
            )
        }
    }

     fun saveSettings() {
        viewModelScope.launch {
            try {
                _state.update { it.copy(isLoading = true) }

                // Create shortcut using ShortcutManager
                createPhoneCallShortcut()

                // Save to repository or preferences if needed
                // iconRepository.saveSettings(_state.value)

                _state.update { it.copy(isLoading = false) }

                // Navigate back or show confirmation
            } catch (e: Exception) {
                _state.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = "Failed to create shortcut: ${e.message}"
                    )
                }
            }
        }
    }

    private fun createPhoneCallShortcut() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            // For pre-Oreo devices, use alternative approach or show message
            throw Exception("Pinned shortcuts require Android 8.0 or higher")
        }

        val shortcutManager = context.getSystemService(Context.SHORTCUT_SERVICE) as ShortcutManager

        if (!shortcutManager.isRequestPinShortcutSupported) {
            throw Exception("Device doesn't support pin shortcuts")
        }

        val currentState = _state.value

        val customIcon = createCustomIcon(currentState)

        // Create intent for phone dialer
        val dialerIntent = Intent(Intent.ACTION_DIAL).apply {
            addCategory(Intent.CATEGORY_DEFAULT)
        }

        val shortcutInfo = ShortcutInfo.Builder(context, "phone_call_shortcut")
            .setShortLabel("\uD800\uDF03\uD800\uDF0C\uD800\uDF0C")
            .setLongLabel("Phone Call")
            .setIcon(Icon.createWithBitmap(customIcon))
            .setIntent(dialerIntent)
            .build()
        // Create callback for when shortcut is pinned
        val callback = PendingIntent.getBroadcast(
            context,
            0,
            Intent(context, ShortcutCreatedReceiver::class.java),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        shortcutManager.requestPinShortcut(shortcutInfo, callback.intentSender)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createCustomIcon(state: CustomizeIconState): Bitmap {
        val iconSize = 96 // dp
        val bitmap = createBitmap(iconSize, iconSize)
        val canvas = Canvas(bitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        // Draw background
        when (state.backgroundColorMode) {
            BackgroundColorMode.SOLID -> {
                paint.color = state.selectedBackgroundColor.toArgb()
                canvas.drawRect(0f, 0f, iconSize.toFloat(), iconSize.toFloat(), paint)
            }

            BackgroundColorMode.GRADIENT -> {
                state.backgroundGradient?.let { brush ->
                    // Convert Compose Brush to Canvas gradient
                    paint.shader = LinearGradient(
                        0f, 0f, iconSize.toFloat(), iconSize.toFloat(),
                        intArrayOf(
                            state.selectedBackgroundColor.toArgb(),
                            Color.White.toArgb()
                        ),
                        null,
                        Shader.TileMode.CLAMP
                    )
                    canvas.drawRect(0f, 0f, iconSize.toFloat(), iconSize.toFloat(), paint)
                }
            }
        }

        if (state.backgroundImage != null) {
            paint.shader = null

            try {
                val softwareBitmap = if (state.backgroundImage.config == Bitmap.Config.HARDWARE) {
                    state.backgroundImage.copy(Bitmap.Config.ARGB_8888, false)
                } else {
                    state.backgroundImage
                }

                val scaledBitmap = centerCropBitmap(softwareBitmap, iconSize, iconSize)
                canvas.drawBitmap(scaledBitmap, 0f, 0f, paint)

                if (softwareBitmap != state.backgroundImage) {
                    softwareBitmap.recycle()
                }
            } catch (_: Exception) {
                paint.color = state.selectedBackgroundColor.toArgb()
                canvas.drawRect(0f, 0f, iconSize.toFloat(), iconSize.toFloat(), paint)
            }
        }

        // Draw phone icon symbol
        paint.shader = null
        paint.color = state.selectedSymbolColor.toArgb()
        paint.textSize = iconSize * 0.6f
        paint.textAlign = Paint.Align.CENTER

        // Get selected font
        val selectedFont = state.fonts.find { it.isSelected }
        selectedFont?.let { font ->
            // Apply font family if needed
            // paint.typeface = getTypefaceForFont(font.id)
        }

        // Draw phone symbol
        val phoneSymbol = "📞"
        val textBounds = Rect()
        paint.getTextBounds(phoneSymbol, 0, phoneSymbol.length, textBounds)

        canvas.drawText(
            phoneSymbol,
            iconSize / 2f,
            iconSize / 2f + textBounds.height() / 2f,
            paint
        )

        return bitmap
    }

    private fun centerCropBitmap(source: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        val sourceWidth = source.width
        val sourceHeight = source.height

        // Compute the scaling factors
        val xScale = targetWidth.toFloat() / sourceWidth
        val yScale = targetHeight.toFloat() / sourceHeight
        val scale = maxOf(xScale, yScale)

        // Scale dimensions
        val scaledWidth = (sourceWidth * scale).toInt()
        val scaledHeight = (sourceHeight * scale).toInt()

        // Determine crop coordinates
        val startX = (scaledWidth - targetWidth) / 2
        val startY = (scaledHeight - targetHeight) / 2

        // Create the result bitmap
        val result = createBitmap(targetWidth, targetHeight)
        val canvas = Canvas(result)

        // Scale and crop
        val scaledBitmap = source.scale(scaledWidth, scaledHeight)
        val src = Rect(startX, startY, startX + targetWidth, startY + targetHeight)
        val dst = Rect(0, 0, targetWidth, targetHeight)

        canvas.drawBitmap(scaledBitmap, src, dst, null)

        // Clean up
        if (scaledBitmap != source) {
            scaledBitmap.recycle()
        }

        return result
    }

    // Helper function to convert Compose Color to Android Color
    private fun androidx.compose.ui.graphics.Color.toArgb(): Int {
        return android.graphics.Color.argb(
            (this.alpha * 255).toInt(),
            (this.red * 255).toInt(),
            (this.green * 255).toInt(),
            (this.blue * 255).toInt()
        )
    }


    private fun guess(num: Int): Int {
        // This is a placeholder for the predefined API
        return 0 // Replace with actual implementation
    }
}