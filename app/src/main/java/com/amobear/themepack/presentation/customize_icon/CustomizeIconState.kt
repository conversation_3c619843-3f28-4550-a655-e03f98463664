package com.amobear.themepack.presentation.customize_icon

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Shader
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.LinearGradient
import androidx.compose.ui.graphics.RadialGradient
import androidx.compose.ui.graphics.toArgb
import androidx.core.graphics.scale
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import com.amobear.themepack.utils.font.UnicodeTextTransformer

data class CustomizeIconState(
    val fonts: ImmutableList<FontOption> = persistentListOf(),
    val selectedSymbolColor: Color = Color.Black,
    val selectedBackgroundColor: Color = Color.White,
    val backgroundGradient: Brush? = null,
    val gradientColors: List<Color> = emptyList(),
    val symbolColorAlpha: Float = 0f,
    val backgroundColorAlpha: Float = 0f,
    val backgroundColorMode: BackgroundColorMode = BackgroundColorMode.SOLID,
    val backgroundImage: Bitmap? = null,
    val isLoading: Boolean = false,
    val errorMessage: String? = null
) {
    companion object {
        val initialCustomizeIconState = CustomizeIconState(
            // Populated by loadDefaultSettings
            fonts = UnicodeTextTransformer.MathStyle.entries.mapIndexed { index, font ->
                FontOption(
                    fontStyle = font,
                    isSelected = index == 0
                )
            }.toImmutableList(),
            // Reflects the selection made in loadDefaultSettings

            // Uses default value from data class definition as loadDefaultSettings doesn't modify it
            // Uses default value from data class definition
            symbolColorAlpha = 0.5f,

            // Uses default value from data class definition
            selectedSymbolColor = Color.Black.copy(alpha = 0.5f),

            // Uses default value from data class definition as loadDefaultSettings doesn't modify it
            // Uses default value from data class definition
            backgroundColorAlpha = 0.5f,
            // Uses default value from data class definition
            selectedBackgroundColor = Color.Green.copy(alpha = 0.5f),

            isLoading = false,
            errorMessage = null
        )
    }

    fun drawGradientBackground(canvas: Canvas, colorList: List<Color>, size: Int) {
        val paint = Paint().apply {
            isAntiAlias = true
        }
        val shader = android.graphics.LinearGradient(
            0f, 0f,
            0f, size.toFloat(),
            colorList.map { it.toArgb() }.toIntArray(),
            null,
            Shader.TileMode.CLAMP
        )
        paint.shader = shader
        canvas.drawRect(0f, 0f, size.toFloat(), size.toFloat(), paint)
    }
}

fun CustomizeIconState.shouldDrawOnlyBackgroundImage(): Boolean {
    return backgroundImage != null &&
            selectedBackgroundColor == Color.White &&
            backgroundColorAlpha == 0f &&
            backgroundColorMode == BackgroundColorMode.SOLID &&
            backgroundGradient == null &&
            gradientColors.isEmpty()
}

data class FontOption(
    val fontStyle: UnicodeTextTransformer.MathStyle,
    val isSelected: Boolean = false
) {
    companion object {
        val defaultFont = FontOption(UnicodeTextTransformer.MathStyle.NORMAL)
    }
}

enum class BackgroundColorMode {
    SOLID,
    GRADIENT
}

fun CustomizeIconState.drawBackground(canvas: Canvas, size: Int) {
    when {
        backgroundImage != null -> {
            // First draw the background color/gradient
            if (backgroundColorMode == BackgroundColorMode.GRADIENT && backgroundGradient != null) {
                drawGradientBackground(canvas = canvas, size = size, colorList = gradientColors)
            } else {
                val paint = Paint().apply {
                    color = selectedBackgroundColor.copy(alpha = backgroundColorAlpha).toArgb()
                    isAntiAlias = true
                }
                canvas.drawRect(0f, 0f, size.toFloat(), size.toFloat(), paint)
            }

            // Then draw the background image on top
            val imagePaint = Paint().apply {
                alpha = (backgroundColorAlpha * 255).toInt()
                isAntiAlias = true
                isFilterBitmap = true
            }
            val safeBitmap = if (backgroundImage.config != Bitmap.Config.ARGB_8888) {
                backgroundImage.copy(Bitmap.Config.ARGB_8888, false)
            } else {
                backgroundImage
            }
            val scaledBackground = safeBitmap.scale(size, size)
            canvas.drawBitmap(scaledBackground, 0f, 0f, imagePaint)
        }

        backgroundColorMode == BackgroundColorMode.GRADIENT && backgroundGradient != null -> {
            drawGradientBackground(canvas = canvas, size = size, colorList = gradientColors)
        }

        else -> {
            val paint = Paint().apply {
                color = selectedBackgroundColor.copy(alpha = backgroundColorAlpha).toArgb()
                isAntiAlias = true
            }
            canvas.drawRect(0f, 0f, size.toFloat(), size.toFloat(), paint)
        }
    }
}

val gradientPairs = persistentListOf(

    // 🌈 RAINBOW & SPECTRUM EXPLOSIONS
    Triple(
        Color(0xFFFF0080),
        Color(0xFF8000FF),
        Color(0xFF0080FF)
    ), // Hot Pink → Purple → Electric Blue
    Triple(
        Color(0xFFFF4500),
        Color(0xFFFFD700),
        Color(0xFF32CD32)
    ), // Orange Red → Gold → Lime Green
    Triple(Color(0xFF00FFFF), Color(0xFFFF00FF), Color(0xFFFFFF00)), // Cyan → Magenta → Yellow
    Triple(
        Color(0xFF7F00FF),
        Color(0xFF00FF7F),
        Color(0xFFFF7F00)
    ), // Violet → Spring Green → Orange

    // 🔥 FIRE & PLASMA
    Triple(
        Color(0xFFFF1744),
        Color(0xFFFF6D00),
        Color(0xFFFFD600)
    ), // Crimson → Orange → Electric Yellow
    Triple(
        Color(0xFFD50000),
        Color(0xFFFF3D00),
        Color(0xFFFF8F00)
    ), // Dark Red → Red Orange → Amber
    Triple(
        Color(0xFF6A1B9A),
        Color(0xFFE91E63),
        Color(0xFFFF5722)
    ), // Deep Purple → Pink → Deep Orange
    Triple(Color(0xFF000051), Color(0xFF304FFE), Color(0xFF40C4FF)), // Navy → Royal Blue → Sky Blue

    // 🌊 OCEAN DEPTHS & AQUATIC
//    Triple(Color(0xFF006064), Color(0xFF00ACC1), Color(0xFF4FC3F7)), // Deep Teal → Cyan → Light Blue
//    Triple(Color(0xFF1A237E), Color(0xFF3F51B5), Color(0xFF2196F3)), // Deep Blue → Indigo → Blue
//    Triple(Color(0xFF004D40), Color(0xFF26A69A), Color(0xFF80CBC4)), // Dark Teal → Teal → Light Teal
//    Triple(Color(0xFF0D47A1), Color(0xFF1976D2), Color(0xFF42A5F5)), // Navy → Blue → Light Blue

    // 🌸 NEON & CYBERPUNK
    Triple(
        Color(0xFFE1006E),
        Color(0xFF00E1D9),
        Color(0xFFE1E100)
    ), // Neon Pink → Neon Cyan → Neon Yellow
    Triple(Color(0xFF00FF41), Color(0xFF41FF00), Color(0xFFFF0041)), // Neon Green → Lime → Neon Red
    Triple(
        Color(0xFF8E24AA),
        Color(0xFFE040FB),
        Color(0xFF00E5FF)
    ), // Purple → Bright Magenta → Cyan
    Triple(
        Color(0xFF212121),
        Color(0xFF00E676),
        Color(0xFF76FF03)
    ), // Dark Gray → Neon Green → Lime

    // 🌅 EXOTIC SUNSETS
    Triple(Color(0xFF880E4F), Color(0xFFFF5722), Color(0xFFFFEB3B)), // Deep Pink → Orange → Yellow
    Triple(Color(0xFF3E2723), Color(0xFFFF5722), Color(0xFFFF9800)), // Brown → Orange → Amber
    Triple(Color(0xFF1A237E), Color(0xFFAD1457), Color(0xFFFF6F00)), // Navy → Pink → Orange
    Triple(Color(0xFF4A148C), Color(0xFFE91E63), Color(0xFFFFAB00)), // Deep Purple → Pink → Amber

    // 🎨 ARTISTIC & CREATIVE
    Triple(Color(0xFF795548), Color(0xFFFF7043), Color(0xFFFFCA28)), // Brown → Coral → Yellow
    Triple(
        Color(0xFF37474F),
        Color(0xFF26C6DA),
        Color(0xFFABF7FF)
    ), // Blue Gray → Cyan → Light Cyan
    Triple(Color(0xFF6A4C93), Color(0xFFC06C84), Color(0xFFF8B500)), // Purple → Rose → Golden
    Triple(
        Color(0xFF2E8B57),
        Color(0xFF32CD32),
        Color(0xFF9AFF9A)
    ), // Sea Green → Lime Green → Pale Green

    // 🌌 COSMIC & GALACTIC
//    Triple(Color(0xFF0D1117), Color(0xFF6F42C1), Color(0xFFBB86FC)), // Space Black → Galaxy Purple → Lavender
//    Triple(Color(0xFF1B1B2F), Color(0xFF162447), Color(0xFF1F4287)), // Dark Navy → Deep Blue → Blue
//    Triple(Color(0xFF2D1B69), Color(0xFF11998E), Color(0xFF38EF7D)), // Deep Purple → Teal → Green
//    Triple(Color(0xFF000428), Color(0xFF004E92), Color(0xFF009FFF)), // Space Navy → Ocean Blue → Bright Blue

    // 🍭 CANDY & SWEET
    Triple(Color(0xFFFF6B9D), Color(0xFFC44569), Color(0xFF6C5CE7)), // Pink → Rose → Purple
    Triple(Color(0xFFFFD93D), Color(0xFFFF6B9D), Color(0xFF6C5CE7)), // Yellow → Pink → Purple
    Triple(
        Color(0xFFFF9FF3),
        Color(0xFFF368E0),
        Color(0xFFE056FD)
    ), // Light Pink → Hot Pink → Purple
    Triple(
        Color(0xFF74EBD5),
        Color(0xFFACB6E5),
        Color(0xFFFFE47D)
    ), // Turquoise → Lavender → Yellow

    // 🌟 METALLIC & PREMIUM
    Triple(
        Color(0xFFB8860B),
        Color(0xFFFFD700),
        Color(0xFFFFFACD)
    ), // Dark Gold → Gold → Light Gold
    Triple(Color(0xFF2F4F4F), Color(0xFFC0C0C0), Color(0xFFFFFFFF)), // Dark Slate → Silver → White
    Triple(
        Color(0xFF8B4513),
        Color(0xFFCD853F),
        Color(0xFFDEB887)
    ), // Saddle Brown → Peru → Burlywood
    Triple(
        Color(0xFF4B0082),
        Color(0xFF9932CC),
        Color(0xFFDA70D6)
    ), // Indigo → Dark Orchid → Orchid
//
//    // 🔮 MYSTICAL & MAGICAL
//    Triple(Color(0xFF1E0342), Color(0xFF6A0572), Color(0xFFAB83A1)), // Deep Purple → Purple → Mauve
//    Triple(Color(0xFF0F3460), Color(0xFF16537E), Color(0xFF533483)), // Navy → Blue → Purple
//    Triple(Color(0xFF2D1B69), Color(0xFF8B5A2B), Color(0xFFFF6B35)), // Purple → Brown → Orange
//    Triple(Color(0xFF360033), Color(0xFF0B8793), Color(0xFF96DEDA)), // Dark Purple → Teal → Light Teal

    // 🌺 TROPICAL & EXOTIC
    Triple(Color(0xFFFF416C), Color(0xFFFF4B2B), Color(0xFFFFDD00)), // Coral → Red Orange → Gold
    Triple(Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFFF093FB)), // Blue → Purple → Pink
    Triple(Color(0xFF21D4FD), Color(0xFFB721FF), Color(0xFFFF21A0)), // Sky Blue → Purple → Pink
    Triple(Color(0xFF3B41C5), Color(0xFFA981BB), Color(0xFFFFC8A2)), // Blue → Purple → Peach

    // 🎮 GAMING & DIGITAL
//    Triple(Color(0xFF00D2FF), Color(0xFF3A7BD5), Color(0xFF6B46C1)), // Electric Blue → Blue → Purple
//    Triple(Color(0xFF0FF0FC), Color(0xFF9C40FF), Color(0xFFFFAC40)), // Cyan → Purple → Orange
//    Triple(Color(0xFF40E0D0), Color(0xFFFF8C94), Color(0xFFFFD3A5)), // Turquoise → Coral → Peach
//    Triple(Color(0xFF667DB6), Color(0xFF0082C8), Color(0xFF667DB6)), // Blue gradient loop

    // 🌈 HOLOGRAPHIC & IRIDESCENT
    Triple(Color(0xFFFF006E), Color(0xFF8338EC), Color(0xFF3A86FF)), // Hot Pink → Purple → Blue
    Triple(Color(0xFFFFBE0B), Color(0xFFFB5607), Color(0xFFFF006E)), // Yellow → Orange → Pink
    Triple(Color(0xFF8338EC), Color(0xFF3A86FF), Color(0xFF06FFA5)), // Purple → Blue → Green
    Triple(Color(0xFFFF7D00), Color(0xFFFFD23F), Color(0xFFFF006E)), // Orange → Yellow → Pink

    // 🌸 PASTEL ENHANCED
    Triple(Color(0xFFFFADCD), Color(0xFFFFC8DD), Color(0xFFFFAFCC)), // Rose Pastels
    Triple(Color(0xFFCDB4DB), Color(0xFFFFC8DD), Color(0xFFFFAFCC)), // Purple to Pink Pastels
    Triple(Color(0xFFA2D2FF), Color(0xFFBDE0FF), Color(0xFFFFAFCC)), // Blue to Pink Pastels
    Triple(Color(0xFFCCF381), Color(0xFF4ECDC4), Color(0xFFFFE66D)) // Green to Yellow Pastels
)

@Composable
fun createGradientBrushes(): ImmutableList<Brush> = remember {
    gradientPairs.map { (startColor, midColor, endColor) ->
        Brush.linearGradient(
            colors = listOf(startColor, midColor, endColor)
        )
    }.toImmutableList()
}

val defaultColors = persistentListOf(
    Color.Black,
    Color(0xFF333333),
    Color(0xFF666666), // Medium Gray
    Color(0xFFAAAAAA), // Light Gray
    Color.White,
    Color(0xFFFFF9C4), // Very Light Yellow
    Color(0xFFFFEB3B), // Yellow
    Color(0xFFFFC107), // Amber
    Color(0xFFFF9800), // Orange
    Color(0xFFFF5722), // Deep Orange
    Color(0xFFF44336), // Red
    Color(0xFFE91E63), // Pink
    Color(0xFFEC407A), // Light Pink
    Color(0xFFF48FB1), // Very Light Pink
    Color(0xFFF8BBD0), // Pale Pink
    Color(0xFFCE93D8), // Light Purple
    Color(0xFFBA68C8), // Medium Purple
    Color(0xFF9C27B0), // Purple
    Color(0xFF7B1FA2), // Deep Purple
    Color(0xFF673AB7), // Dark Purple
    Color(0xFF5E35B1), // Indigo-Purple
    Color(0xFF3F51B5), // Indigo
    Color(0xFF3949AB), // Dark Indigo
    Color(0xFF2196F3), // Blue
    Color(0xFF03A9F4), // Light Blue
    Color(0xFF00BCD4), // Cyan
    Color(0xFF26C6DA), // Light Cyan
    Color(0xFF4DD0E1), // Very Light Cyan
    Color(0xFF4CAF50), // Green
    Color(0xFF66BB6A), // Light Green
    Color(0xFF8BC34A), // Lime Green
    Color(0xFFAED581), // Light Lime
    Color(0xFFCDDC39), // Lime
    Color(0xFFDCE775), // Light Lime Yellow
    Color(0xFFF0F4C3)  // Very Light Yellow
)