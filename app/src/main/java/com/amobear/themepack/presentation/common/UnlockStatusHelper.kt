package com.amobear.themepack.presentation.common

import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.model.WidgetPack
import com.amobear.themepack.data.model.IconPack
import com.amobear.themepack.data.repository.ImageBasedUnlockRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for UI components to check unlock status of content
 * 
 * Since domain models no longer contain unlock status fields,
 * UI components use this helper to determine unlock status dynamically
 * via image-based detection.
 */
@Singleton
class UnlockStatusHelper @Inject constructor(
    private val imageBasedUnlockRepository: ImageBasedUnlockRepository
) {
    
    /**
     * Check if a theme is unlocked
     */
    fun isThemeUnlocked(theme: Theme): <PERSON><PERSON>an {
        return imageBasedUnlockRepository.isContentUnlocked(theme.previewImage)
    }
    
    /**
     * Check if a wallpaper pack is purchased
     */
    fun isWallpaperPackPurchased(pack: WallpaperPack): Bo<PERSON>an {
        return imageBasedUnlockRepository.isContentUnlocked(pack.previewImage)
    }
    
    /**
     * Check if a widget pack is purchased
     */
    fun isWidgetPackPurchased(pack: WidgetPack): Boolean {
        return imageBasedUnlockRepository.isContentUnlocked(pack.previewImage)
    }
    
    /**
     * Check if an icon pack is purchased
     */
    fun isIconPackPurchased(pack: IconPack): Boolean {
        return imageBasedUnlockRepository.isContentUnlocked(pack.previewImage)
    }
    
    /**
     * Get unlock status for multiple themes
     */
    fun getThemeUnlockStatuses(themes: List<Theme>): Map<String, Boolean> {
        return themes.associate { theme ->
            theme.previewImage to isThemeUnlocked(theme)
        }
    }
    
    /**
     * Get purchase status for multiple wallpaper packs
     */
    fun getWallpaperPackPurchaseStatuses(packs: List<WallpaperPack>): Map<String, Boolean> {
        return packs.associate { pack ->
            pack.previewImage to isWallpaperPackPurchased(pack)
        }
    }
    
    /**
     * Get purchase status for multiple widget packs
     */
    fun getWidgetPackPurchaseStatuses(packs: List<WidgetPack>): Map<String, Boolean> {
        return packs.associate { pack ->
            pack.previewImage to isWidgetPackPurchased(pack)
        }
    }
    
    /**
     * Get purchase status for multiple icon packs
     */
    fun getIconPackPurchaseStatuses(packs: List<IconPack>): Map<String, Boolean> {
        return packs.associate { pack ->
            pack.previewImage to isIconPackPurchased(pack)
        }
    }
    
    /**
     * Check if user has any unlocked content
     */
    fun hasAnyUnlockedContent(): Boolean {
        return imageBasedUnlockRepository.hasUnlockedContent()
    }
}
