package com.amobear.themepack.presentation.app_install

import com.amobear.themepack.utils.font.UnicodeTextTransformer.transformText
import android.app.Activity
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.graphics.drawable.Icon
import android.os.Build
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.content.pm.ShortcutInfoCompat
import androidx.core.content.pm.ShortcutManagerCompat
import androidx.core.graphics.createBitmap
import androidx.core.graphics.drawable.IconCompat
import com.amobear.themepack.presentation.customize_icon.CustomizeIconState
import com.amobear.themepack.presentation.customize_icon.ShortcutCallbackRegistry
import com.amobear.themepack.presentation.customize_icon.ShortcutCreatedReceiver
import com.amobear.themepack.presentation.customize_icon.drawBackground
import javax.inject.Inject
import android.os.Handler
import android.os.Looper
import androidx.core.graphics.scale
import com.amobear.themepack.presentation.customize_icon.shouldDrawOnlyBackgroundImage
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class ShortcutCreator @Inject constructor(
) {

    fun createHomeScreenShortcut(
        context: Context,
        packageName: String,
        state: CustomizeIconState,
        customLabel: String? = null,
        onResult: ((Boolean, String?) -> Unit)? = null
    ): Boolean {
        return try {
            val packageManager = context.packageManager

            val appInfo = try {
                packageManager.getApplicationInfo(packageName, 0)
            } catch (e: Exception) {
                onResult?.invoke(false, "Failed to get app info: ${e.message}")
                return false
            }

            val intent = Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_LAUNCHER)
            intent.setPackage(packageName)

            val resolveInfo = packageManager.queryIntentActivities(intent, 0).firstOrNull()
            if (resolveInfo == null) {
                onResult?.invoke(false, "No launchable activity found for this app")
                return false
            }

            val originalAppName = packageManager.getApplicationLabel(appInfo).toString()
            val transformedLabel = getTransformedLabel(state, customLabel ?: originalAppName)

            val customIcon = createCustomIcon(context, state, packageName)

            val shortcutId = generateShortcutId(packageName, transformedLabel)
            val mainActivity = resolveInfo.activityInfo.name

            onResult?.let { callback ->
                ShortcutCallbackRegistry.registerCallback(shortcutId, callback)
            }
            saveBitmapToFile(context = context, bitmap = customIcon)
            when {
                Build.VERSION.SDK_INT in 24..25 -> {
                    createAndroid7Shortcut(
                        context,
                        packageName,
                        mainActivity,
                        transformedLabel,
                        customIcon,
                        shortcutId,
                        onResult
                    )
                }

                Build.VERSION.SDK_INT < 24 -> {
                    createLegacyShortcut(
                        context,
                        packageName,
                        mainActivity,
                        transformedLabel,
                        customIcon,
                        shortcutId,
                        onResult
                    )
                }

                else -> {
                    createModernShortcut(
                        context,
                        packageName,
                        mainActivity,
                        transformedLabel,
                        customIcon,
                        shortcutId,
                        onResult
                    )
                }
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            onResult?.invoke(false, e.message)
            Toast.makeText(context, "Failed to create shortcut: ${e.message}", Toast.LENGTH_SHORT)
                .show()
            false
        }
    }

    private fun generateShortcutId(packageName: String, label: String): String {
        return "${packageName}_${label.hashCode()}_${System.currentTimeMillis()}"
    }

    // Android 7.0-7.1 (API 24-25) - Legacy Broadcast Method
    private fun createAndroid7Shortcut(
        context: Context,
        packageName: String,
        mainActivity: String,
        label: String,
        icon: Bitmap,
        shortcutId: String,
        onResult: ((Boolean, String?) -> Unit)? = null
    ) {
        try {
            val proxyIntent = Intent().apply {
                action = Intent.ACTION_MAIN
                addCategory(Intent.CATEGORY_LAUNCHER)
                component = ComponentName(
                    context.packageName,
                    PROXY_ACTIVITY_NAME
                )
                putExtra("packageName", packageName)
                putExtra("mainActivity", mainActivity)
                putExtra("duplicate", false)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED or Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
            }

            val shortcutIntent = Intent().apply {
                putExtra("android.intent.extra.shortcut.ICON", icon)
                putExtra("android.intent.extra.shortcut.INTENT", proxyIntent)
                putExtra("android.intent.extra.shortcut.NAME", label)
                putExtra("duplicate", false)
                action = "com.android.launcher.action.INSTALL_SHORTCUT"
            }

            context.sendBroadcast(shortcutIntent)

            // For Android 7, we can't reliably detect success, so assume success after a delay
            Handler(Looper.getMainLooper()).postDelayed({
                ShortcutCallbackRegistry.invokeCallback(
                    shortcutId,
                    true,
                    "Android 7 shortcut creation completed"
                )
            }, 1000) // 1 second delay

        } catch (e: Exception) {
            e.printStackTrace()
            ShortcutCallbackRegistry.invokeCallback(
                shortcutId,
                false,
                "Failed to create Android 7 shortcut: ${e.message}"
            )
        }
    }

    // Android < 7.0 (API < 24) - Support Library Method
    private fun createLegacyShortcut(
        context: Context,
        packageName: String,
        mainActivity: String,
        label: String,
        icon: Bitmap,
        shortcutId: String,
        onResult: ((Boolean, String?) -> Unit)? = null
    ) {
        try {
            if (!ShortcutManagerCompat.isRequestPinShortcutSupported(context)) {
                ShortcutCallbackRegistry.invokeCallback(
                    shortcutId,
                    false,
                    "Pin shortcuts not supported on this device"
                )
                return
            }

            // Create proxy intent
            val proxyIntent = Intent().apply {
                action = Intent.ACTION_MAIN
                addCategory(Intent.CATEGORY_LAUNCHER)
                component = ComponentName(
                    context.packageName,
                    PROXY_ACTIVITY_NAME
                )
                putExtra("packageName", packageName)
                putExtra("mainActivity", mainActivity)
                putExtra("duplicate", false)
            }

            val shortcutInfoCompat = ShortcutInfoCompat.Builder(context, shortcutId)
                .setShortLabel(label)
                .setLongLabel(label)
                .setIcon(IconCompat.createWithBitmap(icon))
                .setIntent(proxyIntent)
                .apply {
                    val manufacturer = Build.MANUFACTURER.lowercase()
                    if (manufacturer != "google" && manufacturer != "nokia") {
                        setActivity(
                            ComponentName(
                                context.packageName,
                                PROXY_ACTIVITY_NAME
                            )
                        )
                    }
                }
                .build()

            val successCallback = Intent(context, ShortcutCreatedReceiver::class.java).apply {
                action = SHORTCUT_CREATION_SUCCESS
                putExtra(ShortcutCreatedReceiver.EXTRA_SHORTCUT_ID, shortcutId)
            }

            val callbackPendingIntent = PendingIntent.getBroadcast(
                context,
                shortcutId.hashCode(),
                successCallback,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            ShortcutManagerCompat.requestPinShortcut(
                context,
                shortcutInfoCompat,
                callbackPendingIntent.intentSender
            )


        } catch (e: Exception) {
            e.printStackTrace()
            ShortcutCallbackRegistry.invokeCallback(
                shortcutId,
                false,
                "Failed to create legacy shortcut: ${e.message}"
            )
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createModernShortcut(
        context: Context,
        packageName: String,
        mainActivity: String,
        label: String,
        icon: Bitmap,
        shortcutId: String,
        onResult: ((Boolean, String?) -> Unit)? = null
    ) {
        try {
            if (context is Activity) {
                val intent = context.intent
                if (intent?.action == Intent.ACTION_CREATE_SHORTCUT) {
                    val proxyIntent = Intent().apply {
                        action = Intent.ACTION_MAIN
                        addCategory(Intent.CATEGORY_LAUNCHER)
                        component = ComponentName(
                            context.packageName,
                            PROXY_ACTIVITY_NAME
                        )
                        putExtra("packageName", packageName)
                        putExtra("mainActivity", mainActivity)
                        putExtra("duplicate", false)
                    }

                    val resultIntent =
                        Intent("com.android.launcher.action.INSTALL_SHORTCUT").apply {
                            putExtra("android.intent.extra.shortcut.NAME", label)
                            putExtra("android.intent.extra.shortcut.ICON", icon)
                            putExtra("android.intent.extra.shortcut.INTENT", proxyIntent)
                        }

                    context.setResult(Activity.RESULT_OK, resultIntent)
                    context.finish()

                    // For CREATE_SHORTCUT action, assume success
                    ShortcutCallbackRegistry.invokeCallback(
                        shortcutId,
                        true,
                        "Shortcut created via CREATE_SHORTCUT action"
                    )
                    return
                }
            }

            val shortcutManager = context.getSystemService(ShortcutManager::class.java)

            if (!ShortcutManagerCompat.isRequestPinShortcutSupported(context)) {
                ShortcutCallbackRegistry.invokeCallback(
                    shortcutId,
                    false,
                    "Pin shortcuts not supported on this device"
                )
                return
            }

            val proxyIntent = Intent().apply {
                action = Intent.ACTION_MAIN
                addCategory(Intent.CATEGORY_LAUNCHER)
                component = ComponentName(
                    context.packageName,
                    PROXY_ACTIVITY_NAME
                )
                putExtra("packageName", packageName)
                putExtra("mainActivity", mainActivity)
                putExtra("duplicate", false)
            }

            val shortcutInfo = ShortcutInfo.Builder(context, shortcutId)
                .setShortLabel(label)
                .setLongLabel(label)
                .setIcon(Icon.createWithBitmap(icon))
                .setIntent(proxyIntent)
                .apply {
                    val manufacturer = Build.MANUFACTURER.lowercase()
                    if (manufacturer != "google" && manufacturer != "nokia") {
                        setActivity(
                            ComponentName(
                                context.packageName,
                                PROXY_ACTIVITY_NAME
                            )
                        )
                    }
                }
                .build()

            val successCallback = Intent(context, ShortcutCreatedReceiver::class.java).apply {
                action = SHORTCUT_CREATION_SUCCESS
                putExtra(ShortcutCreatedReceiver.EXTRA_SHORTCUT_ID, shortcutId)
            }

            val callbackPendingIntent = PendingIntent.getBroadcast(
                context,
                shortcutId.hashCode(),
                successCallback,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            shortcutManager.requestPinShortcut(shortcutInfo, callbackPendingIntent.intentSender)

            // Don't invoke callback here - let the BroadcastReceiver handle it

        } catch (e: Exception) {
            e.printStackTrace()
            ShortcutCallbackRegistry.invokeCallback(
                shortcutId,
                false,
                "Failed to create modern shortcut: ${e.message}"
            )
        }
    }

    private fun getTransformedLabel(state: CustomizeIconState, originalLabel: String): String {
        val selectedFont = state.fonts.firstOrNull { it.isSelected }
            ?: state.fonts.firstOrNull()
            ?: return originalLabel
        return selectedFont.fontStyle.transformText(originalLabel)
    }

    private fun createCustomIcon(
        context: Context,
        state: CustomizeIconState,
        packageName: String
    ): Bitmap {
        val size = 256
        val bitmap = createBitmap(size, size)
        val canvas = Canvas(bitmap)

        if (state.shouldDrawOnlyBackgroundImage()) {
            val paint = Paint().apply {
                isAntiAlias = true
                isFilterBitmap = true
            }
            val safeBitmap = if (state.backgroundImage?.config != Bitmap.Config.ARGB_8888) {
                state.backgroundImage?.copy(Bitmap.Config.ARGB_8888, false)
            } else {
                state.backgroundImage
            }
            val scaledBackground = safeBitmap?.scale(size, size)
            if (scaledBackground != null) {
                canvas.drawBitmap(scaledBackground, 0f, 0f, paint)
            }
        } else {
            val backgroundSize = (size * 0.85f).toInt()
            val backgroundOffset = (size - backgroundSize) / 2

            val backgroundBitmap = createBitmap(backgroundSize, backgroundSize)
            val backgroundCanvas = Canvas(backgroundBitmap)

            state.drawBackground(
                canvas = backgroundCanvas,
                size = backgroundSize
            )
            canvas.drawBitmap(
                backgroundBitmap,
                backgroundOffset.toFloat(),
                backgroundOffset.toFloat(),
                null
            )
            drawAppIcon(context, canvas, packageName, backgroundSize, backgroundOffset)
        }

        return bitmap
    }

    private fun drawAppIcon(
        context: Context,
        canvas: Canvas,
        packageName: String,
        backgroundSize: Int,
        backgroundOffset: Int
    ) {
        try {
            val packageManager = context.packageManager
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            val appIcon = packageManager.getApplicationIcon(appInfo)

            val iconSize = (backgroundSize * 0.6f).toInt()
            val iconOffsetX = backgroundOffset + (backgroundSize - iconSize) / 2
            val iconOffsetY = backgroundOffset + (backgroundSize - iconSize) / 2

            val iconBitmap = drawableToBitmap(appIcon, iconSize, iconSize)

            canvas.drawBitmap(iconBitmap, iconOffsetX.toFloat(), iconOffsetY.toFloat(), null)

        } catch (e: Exception) {
            e.printStackTrace()
            drawPlaceholderIcon(canvas, backgroundSize, backgroundOffset)
        }
    }

    private fun drawableToBitmap(drawable: Drawable, width: Int, height: Int): Bitmap {
        val bitmap = createBitmap(width, height)
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, width, height)
        drawable.draw(canvas)
        return bitmap
    }

    private fun drawPlaceholderIcon(canvas: Canvas, backgroundSize: Int, backgroundOffset: Int) {
        val paint = Paint().apply {
            color = Color.Gray.toArgb()
            isAntiAlias = true
        }

        val iconSize = backgroundSize * 0.6f
        val radius = iconSize / 4
        val centerX = backgroundOffset + backgroundSize / 2f
        val centerY = backgroundOffset + backgroundSize / 2f

        canvas.drawCircle(centerX, centerY, radius, paint)
    }

    private fun saveBitmapToFile(context: Context, bitmap: Bitmap): String {
        val file = File(context.cacheDir, SAVED_ICON_FILE_NAME)
        try {
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
            }
            return file.absolutePath
        } catch (e: IOException) {
            e.printStackTrace()
            return ""
        }
    }


    companion object {
        const val SHORTCUT_CREATION_SUCCESS = "com.amobear.themepack.SHORTCUT_PINNED"
        const val PROXY_ACTIVITY_NAME =
            "com.amobear.themepack.presentation.app_install.ShortcutProxyActivity"
        const val SAVED_ICON_FILE_NAME = "custom_icon_lmao.png"
    }
}

fun CustomizeIconState.createHomeScreenShortcut(
    context: Context,
    packageName: String,
    customLabel: String? = null,
    onResult: ((Boolean, String?) -> Unit)? = null
): Boolean {
    return ShortcutCreator().createHomeScreenShortcut(
        context,
        packageName,
        this,
        customLabel = customLabel,
        onResult = onResult
    )
}