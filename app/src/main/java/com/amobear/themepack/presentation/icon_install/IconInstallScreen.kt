package com.amobear.themepack.presentation.icon_install

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.amobear.themepack.R
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.ui.common.InstallTopAppBar
import com.amobear.themepack.ui.common.StepsIndicator

// --- Legacy Data Class for Preview ---
data class AppIconInfo(
    val id: String,
    val originalAppName: String,
    val originalIconPainter: Int, // Painter resource for original icon (placeholder)
    val newAppName: String,
    val newIconPainter: Int, // Painter resource for new icon (placeholder)
    var isSelected: Boolean,
    val isLocked: Boolean = false // If true, shows "Unlock Watch an ads"
)

// --- Main Screen Composable ---
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IconInstallScreen(
    onBackClick: () -> Unit,
    theme: Theme,
    viewModel: IconInstallViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Load theme data when themeId is provided
    LaunchedEffect(theme) {
        viewModel.processIntent(IconInstallIntent.LoadTheme(theme))
    }

    // Show error message
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(error)
            viewModel.processIntent(IconInstallIntent.ClearError)
        }
    }

    IconInstallScreenContent(uiState, onBackClick, snackbarHostState)
}

@Composable
private fun IconInstallScreenContent(
    uiState: IconInstallUiState,
    onBackClick: () -> Unit,
    snackbarHostState: SnackbarHostState,
    processIntent: (IconInstallIntent) -> Unit = {},
) {
    Scaffold(
        topBar = {
            Column {
                InstallTopAppBar(
                    title = "Install",
                    currentCoins = uiState.currentCoins,
                    onAddCoinsClick = { processIntent(IconInstallIntent.AddCoins) },
                    onBackClick = onBackClick
                )
                StepsIndicator(
                    currentActiveStep = 2,
                    modifier = Modifier.padding(top = 8.dp, bottom = 16.dp)
                )
            }
        },
        bottomBar = {
            IconInstallBottomBar(
                onUnlockAllClick = { processIntent(IconInstallIntent.UnlockAllIcons) }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        containerColor = Color(0xFFF8F8F8)
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center),
                    color = Color(0xFF007AFF)
                )
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(bottom = 16.dp)
                ) {
                    item {
                        FontOfIconSection(
                            fontOptions = uiState.fontOptions,
                            selectedOptionId = uiState.selectedFontOptionId,
                            onOptionSelected = { fontId ->
                                processIntent(IconInstallIntent.SelectFont(fontId))
                            },
                            onSeeMoreClick = {
                                processIntent(IconInstallIntent.ShowMoreFonts)
                            }
                        )
                    }
                    item {
                        Spacer(modifier = Modifier.height(24.dp))
                        ChangeIconSection(
                            appIcons = uiState.appIcons,
                            onSelectAll = {
                                processIntent(IconInstallIntent.SelectAllIcons)
                            },
                            onIconSelected = { iconId ->
                                processIntent(IconInstallIntent.ToggleIconSelection(iconId))
                            },
                            onUnlockSingleIcon = { iconId ->
                                processIntent(IconInstallIntent.UnlockIcon(iconId))
                            },
                            isSelectAllChecked = uiState.selectAllIcons
                        )
                    }
                }
            }
        }
    }
}


// --- Font of Icon Section ---
@Composable
private fun FontOfIconSection(
    fontOptions: List<FontOption>,
    selectedOptionId: Int,
    onOptionSelected: (Int) -> Unit,
    onSeeMoreClick: () -> Unit
) {
    Column(modifier = Modifier.padding(horizontal = 16.dp)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                "Font of icon",
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp,
                color = Color.Black
            )
            TextButton(onClick = onSeeMoreClick) {
                Text("See more", color = Color(0xFF007AFF), fontWeight = FontWeight.Medium)
            }
        }
        Spacer(modifier = Modifier.height(8.dp))
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(10.dp),
            contentPadding = PaddingValues(horizontal = 2.dp)
        ) {
            items(fontOptions) { fontOption ->
                FontPreviewItem(
                    option = fontOption,
                    isSelected = fontOption.id == selectedOptionId,
                    onClick = { onOptionSelected(fontOption.id) }
                )
            }
        }
    }
}

@Composable
private fun FontPreviewItem(
    option: FontOption,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor =
        if (option.isArrow) Color(0xFF007AFF) else if (isSelected) Color(0xFFE0E0E0) else Color(
            0xFFF0F0F0
        )
    val contentColor = if (option.isArrow) Color.White else Color.Black
    val borderColor = if (isSelected && !option.isArrow) Color(0xFF007AFF) else Color.Transparent

    Box(
        modifier = Modifier
            .size(56.dp)
            .clip(CircleShape)
            .background(backgroundColor)
            .border(2.dp, borderColor, CircleShape)
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        if (option.isArrow) {
            Icon(Icons.Filled.ArrowForward, contentDescription = "More fonts", tint = contentColor)
        } else {
            Text(
                "Abc",
                fontSize = 18.sp,
                color = contentColor,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
            )
        }
    }
}

// --- Change Icon Section ---
@Composable
private fun ChangeIconSection(
    appIcons: List<AppIconData>,
    onSelectAll: () -> Unit,
    onIconSelected: (Int) -> Unit,
    onUnlockSingleIcon: (Int) -> Unit,
    isSelectAllChecked: Boolean
) {
    Column(modifier = Modifier.padding(horizontal = 16.dp)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                "Change icon",
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp,
                color = Color.Black
            )
            TextButton(onClick = onSelectAll) {
                Text(
                    if (isSelectAllChecked) "Deselect all" else "Select all",
                    color = Color(0xFF007AFF),
                    fontWeight = FontWeight.Medium
                )
            }
        }
        Spacer(modifier = Modifier.height(12.dp))
        Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            appIcons.forEach { appIcon ->
                IconChangeRow(
                    appInfo = appIcon,
                    onSelected = { onIconSelected(appIcon.id) },
                    onUnlockClick = { onUnlockSingleIcon(appIcon.id) }
                )
            }
        }
    }
}

@Composable
private fun IconChangeRow(
    appInfo: AppIconData,
    onSelected: () -> Unit,
    onUnlockClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(Color.White)
            .padding(horizontal = 12.dp, vertical = 10.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        RadioButton(
            selected = appInfo.isSelected,
            onClick = onSelected,
            colors = RadioButtonDefaults.colors(selectedColor = Color(0xFF007AFF))
        )
        Spacer(modifier = Modifier.width(8.dp))

        AppIconDisplay(
            imageUrl = appInfo.originalIconUrl,
            appName = appInfo.originalAppName,
            isOriginal = true
        )

        Spacer(modifier = Modifier.width(10.dp))
        Icon(
            painterResource(id = R.drawable.ic_double_arrow_right),
            contentDescription = "Transforms to",
            tint = Color.Gray,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(10.dp))

        AppIconDisplay(
            imageUrl = appInfo.newIconUrl,
            appName = appInfo.newAppName,
            isOriginal = false
        )

        Spacer(modifier = Modifier.weight(1f))

        if (appInfo.isLocked) {
            Button(
                onClick = onUnlockClick,
                shape = RoundedCornerShape(8.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF007AFF)),
                contentPadding = PaddingValues(horizontal = 10.dp, vertical = 6.dp),
                modifier = Modifier.height(36.dp)
            ) {
                Icon(
                    painterResource(id = R.drawable.ic_video_play_ads),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        "Unlock",
                        fontSize = 10.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                    Text("Watch an ads", fontSize = 8.sp, color = Color.White)
                }
            }
        } else {
            Box(modifier = Modifier.width(100.dp))
        }
    }
}

@Composable
private fun AppIconDisplay(imageUrl: String?, appName: String, isOriginal: Boolean) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        if (isOriginal) {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .background(Color(0xFFFFEB3B)),
                contentAlignment = Alignment.Center
            ) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(imageUrl)
                        .crossfade(true)
                        .placeholder(R.drawable.ic_launcher_foreground)
                        .error(R.drawable.ic_launcher_foreground)
                        .build(),
                    contentDescription = appName,
                    modifier = Modifier.size(32.dp),
                    contentScale = ContentScale.Fit
                )
            }
        } else {
            Row(
                modifier = Modifier
                    .clip(RoundedCornerShape(10.dp))
                    .background(Color.White)
                    .border(1.dp, Color.LightGray, RoundedCornerShape(10.dp))
                    .padding(horizontal = 6.dp, vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(imageUrl)
                        .crossfade(true)
                        .placeholder(R.drawable.ic_launcher_foreground)
                        .error(R.drawable.ic_launcher_foreground)
                        .build(),
                    contentDescription = appName,
                    modifier = Modifier.size(32.dp),
                    contentScale = ContentScale.Fit
                )
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    painterResource(id = R.drawable.ic_edit_pencil),
                    contentDescription = "Edit",
                    tint = Color.DarkGray,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(4.dp))
        Text(appName, fontSize = 11.sp, color = Color.DarkGray, textAlign = TextAlign.Center)
    }
}

// --- Bottom Bar ---
@Composable
private fun IconInstallBottomBar(onUnlockAllClick: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(16.dp)
    ) {
        Button(
            onClick = onUnlockAllClick,
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp),
            shape = RoundedCornerShape(8.dp),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF015BFF))
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_video_play_ads),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        "Unlock all icon",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    Text(
                        "Watch an ads",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.White
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(10.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp)
                .background(Color(0xFF212232))
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(
                    modifier = Modifier
                        .background(Color.Yellow)
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text("ADS", color = Color.Black, fontWeight = FontWeight.Bold, fontSize = 12.sp)
                }
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Ad Zone: In order to continue developing the app...",
                    color = Color.White,
                    fontSize = 10.sp,
                )
            }
        }
    }
}

// --- Preview ---
@Preview(showBackground = true, device = "spec:width=360dp,height=780dp,dpi=420")
@Composable
fun IconInstallScreenPreview() {
    // Create sample theme data
    val sampleTheme = Theme(
        id = 1,
        title = "Modern Theme",
        description = "A beautiful modern theme with custom icons",
        previewImage = "https://example.com/preview.jpg",
        categoryId = 1,
        wallpaperPacks = emptyList(),
        widgetPacks = emptyList(),
        iconPacks = emptyList(),
        isUnlocked = false,
    )

    // Create sample UI state
    val sampleUiState = IconInstallUiState(
        theme = sampleTheme,
        fontOptions = defaultFontOptions,
        selectedFontOptionId = 1,
        appIcons = listOf(
            AppIconData(
                id = 1,
                appId = "com.google.android.gm",
                originalAppName = "Gmail",
                originalIconUrl = "https://example.com/gmail_original.png",
                newAppName = "Gmail",
                newIconUrl = "https://example.com/gmail_new.png",
                isSelected = true,
                isLocked = false,
                packId = 1
            ),
            AppIconData(
                id = 2,
                appId = "com.amazon.mShop.android.shopping",
                originalAppName = "Amazon",
                originalIconUrl = "https://example.com/amazon_original.png",
                newAppName = "Amazon",
                newIconUrl = "https://example.com/amazon_new.png",
                isSelected = false,
                isLocked = true,
                packId = 1
            ),
            AppIconData(
                id = 3,
                appId = "com.google.android.apps.photos",
                originalAppName = "Photos",
                originalIconUrl = "https://example.com/photos_original.png",
                newAppName = "Photos",
                newIconUrl = "https://example.com/photos_new.png",
                isSelected = false,
                isLocked = false,
                packId = 1
            )
        ),
        currentCoins = 150,
        selectAllIcons = false,
        isLoading = false,
        error = null
    )

    MaterialTheme {
        IconInstallScreenContent(
            uiState = sampleUiState,
            onBackClick = {},
            snackbarHostState = remember { SnackbarHostState() }
        )
    }
} 