package com.amobear.themepack.presentation.icon_setup_complete

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.extension.getSavedIconFilePath
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SetupCompleteViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _state = MutableStateFlow(SetupCompleteState(
        mainIconFilePath = context.getSavedIconFilePath()
    ))
    val state: StateFlow<SetupCompleteState> = _state.asStateFlow()

    init {
        loadIconApps()
    }

    fun processIntent(intent: SetupCompleteIntent) {
        when (intent) {
            is SetupCompleteIntent.SeeMore -> handleSeeMore()
            is SetupCompleteIntent.IconClicked -> handleIconClicked(intent.iconId)
            is SetupCompleteIntent.RefreshIcons -> loadIconApps()
            // Navigation intents are handled in the composable
            else -> { /* Handle other intents if needed */
            }
        }
    }

    private fun handleSeeMore() {}

    private fun loadIconApps() {
//        viewModelScope.launch {
//            try {
//                _state.value = _state.value.copy(isLoading = true)
//
//                // Load featured/default icon apps for the setup complete screen
//                val iconApps = iconRepository.getFeaturedIconApps().map { app ->
//                    IconApp(
//                        id = app.id,
//                        name = app.name,
//                        iconRes = app.iconRes,
//                        backgroundColor = app.backgroundColor,
//                        iconBase64 = app.iconBase64
//                    )
//                }.toPersistentList()
//
//                _state.value = _state.value.copy(
//                    iconApps = iconApps,
//                    isLoading = false
//                )
//
//                // Track analytics
//                analyticsManager?.trackEvent("setup_complete_icons_loaded", mapOf(
//                    "icon_count" to iconApps.size.toString()
//                ))
//
//            } catch (e: Exception) {
//                _state.value = _state.value.copy(
//                    isLoading = false,
//                    error = e.message
//                )
//            }
//        }
    }


    private fun handleIconClicked(iconId: String) {
//        viewModelScope.launch {
//            try {
//                // Mark icon as selected or apply it
//                iconRepository.selectIcon(iconId)
//
//                // Update state to reflect selection
//                val updatedApps = _state.value.iconApps.map { app ->
//                    if (app.id == iconId) {
//                        app.copy(isSelected = true)
//                    } else {
//                        app
//                    }
//                }.toPersistentList()
//
//                _state.value = _state.value.copy(iconApps = updatedApps)
//
//            } catch (e: Exception) {
//                _state.value = _state.value.copy(error = e.message)
//            }
//        }
    }
}