package com.amobear.themepack.presentation.app_install.component

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.amobear.themepack.R
import com.amobear.themepack.designsystem.ThemeAppTheme
import com.amobear.themepack.domain.model.app.AppItem
import com.amobear.themepack.domain.model.app.AppState
import com.amobear.themepack.presentation.app_install.AppInstallDataBundle
import com.amobear.themepack.utils.font.transformTextToSelectedFont

@Composable
fun AppItemCard(
    appItem: AppItem,
    onToggleSelection: () -> Unit,
    onInstall: () -> Unit,
    onWatchAd: () -> Unit,
    onAppSelecting: () -> Unit,
    onAppRename: () -> Unit,
    onDestinationIconDelete: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp, bottom = 4.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        AppItemMainRow(
            appItem = appItem,
            onToggleSelection = onToggleSelection,
            onInstall = onInstall,
            onWatchAd = onWatchAd,
            onAppSelecting = onAppSelecting,
            onAppRename = onAppRename,
            onDestinationIconDelete = onDestinationIconDelete
        )
    }
}

@Composable
private fun AppItemMainRow(
    appItem: AppItem,
    onToggleSelection: () -> Unit,
    onInstall: () -> Unit,
    onWatchAd: () -> Unit,
    onAppSelecting: () -> Unit,
    onAppRename: () -> Unit,
    onDestinationIconDelete: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Checkbox
        CircularCheckbox(
            checked = appItem.isSelected,
            onCheckedChange = { onToggleSelection() },
            checkedColor = ThemeAppTheme.color.mainBlue,
            uncheckedColor = Color.LightGray,
            modifier = Modifier
                .size(24.dp)
                .align(Alignment.CenterVertically)
        )

        Spacer(modifier = Modifier.width(12.dp))

        // Source app column
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.align(Alignment.CenterVertically)
        ) {
            Box(
                modifier = Modifier
                    .background(
                        color = Color(0xFFF6F2F2),
                        shape = RoundedCornerShape(12.dp)
                    )
            ) {
                Row(
                    modifier = Modifier.clickable {
                        onAppRename()
                    }
                ) {
                    AppIcon(
                        customIconUrl = appItem.customIconUrl,
                        modifier = Modifier
                            .size(48.dp)
                            .padding(start = 8.dp),
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Icon(
                        modifier = Modifier.align(Alignment.CenterVertically),
                        painter = painterResource(R.drawable.ic_edit_icon),
                        contentDescription = "Edit icon",
                    )

                    Spacer(modifier = Modifier.width(8.dp))
                }
            }

            Text(
                text = appItem.name.transformTextToSelectedFont().toString(),
                fontSize = 12.sp,
                color = Color.Black,
                modifier = Modifier
                    .padding(top = 4.dp)
                    .width(64.dp),
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
            )
        }

        // Arrow
        Text(
            text = "»",
            fontSize = 20.sp,
            color = Color.Gray,
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .align(Alignment.CenterVertically)
        )

        // Destination app column
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .width(70.dp)
                .align(Alignment.CenterVertically)
        ) {
            Box(
                modifier = Modifier
                    .size(56.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentAlignment = Alignment.Center,
            ) {
                if (appItem.destinationDrawable != null) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(appItem.destinationDrawable)
                                .crossfade(true)
                                .build(),
                            contentDescription = "Destination app icon",
                            contentScale = ContentScale.Crop,
                            modifier = Modifier.fillMaxSize(),
                        )

                        if (appItem.appState == AppState.NOT_INSTALLED_DIRECT ||
                            appItem.appState == AppState.NOT_INSTALLED_AD_REQUIRED
                        ) {
                            Icon(
                                painterResource(R.drawable.ic_mini_corner_closed),
                                contentDescription = "Remove",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(16.dp)
                                    .align(Alignment.TopEnd)
                                    .clickable(onClick = onDestinationIconDelete)
                            )
                        }
                    }
                } else {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                color = Color(0xFFF6F2F2),
                                shape = RoundedCornerShape(12.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        IconButton(
                            onClick = onAppSelecting,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = null,
                                tint = ThemeAppTheme.color.mainBlue
                            )
                        }
                    }
                }
            }

            Text(
                text = appItem.name.transformTextToSelectedFont().toString(),
                fontSize = 12.sp,
                color = Color.Black,
                modifier = Modifier
                    .padding(top = 4.dp)
                    .width(64.dp),
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        // Action button
        ActionButton(
            modifier = Modifier
                .weight(1f)
                .align(Alignment.CenterVertically),
            appState = appItem.appState,
            onInstall = onInstall,
            onWatchAd = onWatchAd
        )
    }
}

@Composable
private fun AppIcon(
    customIconUrl: String?,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(64.dp)
            .padding(vertical = 4.dp)
            .clip(
                RoundedCornerShape(8.dp)
            )
            .then(
                when {
                    AppInstallDataBundle.customizedIconState?.backgroundImage != null -> {
                        Modifier
                            .background(
                                color = Color.Transparent,
                                shape = RoundedCornerShape(16.dp)
                            )
                            .then(
                                Modifier.paint(
                                    painter = rememberAsyncImagePainter(AppInstallDataBundle.customizedIconState?.backgroundImage),
                                    contentScale = ContentScale.Crop
                                )
                            )
                    }

                    AppInstallDataBundle.customizedIconState?.backgroundGradient != null -> {
                        Modifier.background(
                            brush = AppInstallDataBundle.customizedIconState?.backgroundGradient
                                ?: Brush.linearGradient(listOf(Color.White, Color.White)),
                            shape = RoundedCornerShape(16.dp)
                        )
                    }

                    else -> {
                        Modifier.background(
                            color = AppInstallDataBundle.customizedIconState?.selectedBackgroundColor
                                ?: Color.White,
                            shape = RoundedCornerShape(16.dp)
                        )
                    }
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(customIconUrl)
                .crossfade(true)
                .build(),
            contentDescription = "Source Icon",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize(),
            error = painterResource(id = com.google.android.material.R.drawable.mtrl_ic_error),
            placeholder = painterResource(id = com.google.android.material.R.drawable.mtrl_ic_check_mark)
        )
    }
}

@Composable
private fun ActionButton(
    appState: AppState,
    onInstall: () -> Unit,
    onWatchAd: () -> Unit,
    modifier: Modifier = Modifier
) {
    when (appState) {
        AppState.NOT_INSTALLED_AD_REQUIRED -> {
            Button(
                modifier = modifier,
                onClick = onWatchAd,
                colors = ButtonDefaults.buttonColors(
                    containerColor = ThemeAppTheme.color.mainBlue
                ),
                shape = RoundedCornerShape(8.dp),
                contentPadding = PaddingValues(8.dp),
            ) {
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_rewarded),
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column(
                        verticalArrangement = Arrangement.spacedBy((-8).dp)
                    ) {
                        Text(
                            text = "Unlock",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        )
                        Text(
                            text = "watch an ads",
                            fontSize = 10.sp,
                            color = Color.White
                        )
                    }
                }
            }
        }

        AppState.NOT_INSTALLED_DIRECT -> {
            Button(
                onClick = onInstall,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE6F0FF),
                    contentColor = ThemeAppTheme.color.mainBlue
                ),
                shape = RoundedCornerShape(8.dp),
                modifier = modifier
            ) {
                Text(
                    text = "Install",
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                )
            }
        }

        AppState.INSTALLED -> {
            Button(
                onClick = { /* Already installed */ },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFEEEEEE),
                    contentColor = Color.Gray
                ),
                shape = RoundedCornerShape(8.dp),
                modifier = modifier
            ) {
                Text(
                    text = "Installed",
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                )
            }
        }
    }
}

//installed state
@Preview(showBackground = true)
@Composable
fun InstalledAppItemCardPreview() {
    val sampleAppItem = AppItem(
        name = "Lmao123213123123123123123123123123",
        customIconUrl = "",
        destinationDrawable = null,
        packageName = "com.example.sampleapp",
        isSelected = true,
        appState = AppState.NOT_INSTALLED_AD_REQUIRED
    )

    AppItemCard(
        appItem = sampleAppItem,
        onToggleSelection = {},
        onInstall = {},
        onWatchAd = {},
        onAppSelecting = {},
        onAppRename = {},
        onDestinationIconDelete = {}
    )
}

//ads state
@Preview(showBackground = true)
@Composable
fun AppItemCardWithAdPreview() {
    LocalContext.current
    val sampleAppItem = AppItem(
        name = "Lmao",
        customIconUrl = "",
        destinationDrawable = null,
        packageName = "com.example.sampleappad",
        isSelected = true,
        appState = AppState.NOT_INSTALLED_DIRECT
    )

    AppItemCard(
        appItem = sampleAppItem,
        onToggleSelection = {},
        onInstall = {},
        onWatchAd = {},
        onAppSelecting = {},
        onAppRename = {},
        onDestinationIconDelete = {}

    )
}

@Preview(showBackground = true)
@Composable
fun AppItemCardWithoutIconPreview() {
    val sampleAppItem = AppItem(
        name = "Lmao",
        customIconUrl = "",
        destinationDrawable = null,
        packageName = "com.example.sampleappnoicon",
        isSelected = false,
        appState = AppState.INSTALLED
    )

    AppItemCard(
        appItem = sampleAppItem,
        onToggleSelection = {},
        onInstall = {},
        onWatchAd = {},
        onAppSelecting = {},
        onAppRename = {},
        onDestinationIconDelete = {}
    )
}