package com.amobear.themepack.data.mapper

import com.amobear.themepack.data.model.IconCategoryDto
import com.amobear.themepack.data.model.IconItemDTO
import com.amobear.themepack.data.model.IconPackDto
import com.amobear.themepack.data.model.IconResponse
import com.amobear.themepack.domain.model.icon.CategoryItem
import com.amobear.themepack.domain.model.icon.IconItem
import com.amobear.themepack.domain.model.icon.IconSection
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

// Category mapping
fun IconCategoryDto.toDomain(): CategoryItem {
    return CategoryItem(
        id = this.id.toString(),
        name = this.title,
        iconUrl = this.titleIconUrl,
        isSelected = false
    )
}

fun List<IconCategoryDto>.toDomainCategories(): ImmutableList<CategoryItem> {
    return this.map { it.toDomain() }.toImmutableList()
}

// Icon mapping
fun IconItemDTO.toDomain(packTitle: String): IconItem {
    return IconItem(
        id = this.id.toString(),
        name = this.name,
        imageUrl = this.imageUrl,
        category = packTitle,
        isSelected = false,
        appPackageName = this.appId.orEmpty()
    )
}

fun List<IconItemDTO>.toDomainIcons(packTitle: String): ImmutableList<IconItem> {
    return this.map { it.toDomain(packTitle) }.toImmutableList()
}

// IconPack mapping
fun IconPackDto.toDomain(): IconSection {
    val icons = this.icons ?: emptyList()
    return IconSection(
        title = this.title,
        icons = icons.toDomainIcons(this.title),
        hasMore = icons.size >= 8
    )
}

fun List<IconPackDto>.toDomainSections(): ImmutableList<IconSection> {
    return this.mapNotNull { iconPack ->
        if (!iconPack.icons.isNullOrEmpty()) {
            iconPack.toDomain()
        } else {
            null
        }
    }.toImmutableList()
}

// Category to IconSections - CORRECTED FOR NEW JSON STRUCTURE
fun IconCategoryDto.getIconSections(): ImmutableList<IconSection> {
    val iconPacks = this.iconPacks ?: emptyList()
    return iconPacks.toDomainSections()
}

// Response mapping
fun IconResponse.getAllIconSections(): ImmutableList<IconSection> {
    val allIconSections = mutableListOf<IconSection>()

    this.data.categories.forEach { category ->
        allIconSections.addAll(category.getIconSections())
    }

    return allIconSections.toImmutableList()
}

fun IconResponse.getIconSectionsForCategory(categoryId: String): ImmutableList<IconSection> {
    val category = this.data.categories.find { it.id.toString() == categoryId }
        ?: return persistentListOf()

    return category.getIconSections()
}

fun IconResponse.getCategories(): ImmutableList<CategoryItem> {
    return this.data.categories.toDomainCategories()
}

// Helper extensions for state management
fun ImmutableList<CategoryItem>.updateSelection(selectedId: String): ImmutableList<CategoryItem> {
    return this.map { category ->
        category.copy(isSelected = category.id == selectedId)
    }.toImmutableList()
}

fun ImmutableList<IconSection>.updateIconSelection(
    iconId: String,
    category: String
): ImmutableList<IconSection> {
    return this.map { section ->
        val updatedIcons = section.icons.map { icon ->
            icon.copy(isSelected = icon.id == iconId && icon.category == category)
        }.toImmutableList()
        section.copy(icons = updatedIcons)
    }.toImmutableList()
}

fun ImmutableList<IconSection>.getSelectedIcon(): IconItem? {
    return this.flatMap { it.icons }.find { it.isSelected }
}