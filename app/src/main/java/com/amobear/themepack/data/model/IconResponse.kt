package com.amobear.themepack.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class IconResponse(
    @Json(name = "success") val success: <PERSON><PERSON><PERSON>,
    @<PERSON><PERSON>(name = "data") val data: IconData
)

@JsonClass(generateAdapter = true)
data class IconData(
    @Json(name = "categories") val categories: List<IconCategoryDto>
)

@JsonClass(generateAdapter = true)
data class IconCategoryDto(
    @Json(name = "id") val id: Int,
    @Json(name = "title") val title: String,
    @<PERSON><PERSON>(name = "titleIconUrl") val titleIconUrl: String? = null,
    @<PERSON><PERSON>(name = "weight") val weight: Int? = null,
    @Json(name = "iconPacks") val iconPacks: List<IconPackDto>? = null
)

@JsonClass(generateAdapter = true)
data class IconPackDto(
    @<PERSON><PERSON>(name = "id") val id: Int,
    @Json(name = "title") val title: String,
    @Json(name = "previewImage") val previewImage: String? = null,
    @Json(name = "weight") val weight: Int? = null,
    @Json(name = "coin") val coin: Int? = null,
    @Json(name = "categoryId") val categoryId: Int,
    @Json(name = "icons") val icons: List<IconItemDTO>? = null,
    @Json(name = "compatibleThemes") val compatibleThemes: List<CompatibleThemeDto>? = null
)

@JsonClass(generateAdapter = true)
data class CompatibleThemeDto(
    @Json(name = "id") val id: Int,
    @Json(name = "title") val title: String
)
@JsonClass(generateAdapter = true)
data class IconItemDTO(
    @Json(name = "id") val id: Int,
    @Json(name = "appId") val appId: String? = null,
    @Json(name = "name") val name: String,
    @Json(name = "imageUrl") val imageUrl: String? = null,
    @Json(name = "iconPackId") val iconPackId: Int
)