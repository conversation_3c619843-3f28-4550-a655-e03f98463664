package com.amobear.themepack.data.di

import android.content.Context
import com.amobear.themepack.core.DefaultAppCoroutineDispatchers
import com.amobear.themepack.data.data_source.AppInfoDataSource
import com.amobear.themepack.data.data_source.AssetIconDataSource
import com.amobear.themepack.data.repository.AppRepositoryImpl
import com.amobear.themepack.data.repository.IconsRepositoryImpl
import com.amobear.themepack.domain.repository.AppRepository
import com.amobear.themepack.domain.repository.IconsRepository
import com.squareup.moshi.Moshi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DataModule {

    @Provides
    @Singleton
    fun provideAppInfoDataSource(@ApplicationContext context: Context): AppInfoDataSource {
        return AppInfoDataSource(context)
    }

    @Provides
    @Singleton
    fun provideAssetIconDataSource(
        @ApplicationContext context: Context,
        moshi: Moshi
    ): AssetIconDataSource = AssetIconDataSource(context, moshi)

    @Provides
    @Singleton
    fun provideAppRepository(appInfoDataSource: AppInfoDataSource): AppRepository =
        AppRepositoryImpl(appInfoDataSource)

    @Provides
    @Singleton
    fun provideAssetIconRepository(
        assetIconDataSource: AssetIconDataSource
    ): IconsRepository = IconsRepositoryImpl(assetIconDataSource)

}
