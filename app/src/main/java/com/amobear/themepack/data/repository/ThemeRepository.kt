package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.mapper.toDomain
import com.amobear.themepack.data.mapper.toEntity
import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.model.WidgetPack
import com.amobear.themepack.data.model.IconPack
import com.amobear.themepack.data.model.toIconCategory
import com.amobear.themepack.data.model.toIconPack
import com.amobear.themepack.data.model.toThemeCategory
import com.amobear.themepack.data.model.toWidgetCategory
import com.amobear.themepack.data.model.toWidgetPack
import com.amobear.themepack.data.network.ApiResult
import com.amobear.themepack.data.network.ApiService
import com.amobear.themepack.data.network.toApiResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for content management with hybrid approach:
 * - API: Stateless content browsing
 * - Image-based unlock detection: Real-time unlock status
 * - Database: User's personal library only (ProfileScreen)
 */
@Singleton
class ThemeRepository @Inject constructor(
    private val apiService: ApiService,
    private val imageBasedUnlockRepository: ImageBasedUnlockRepository,
    private val userLibraryRepository: UserLibraryRepository,
) {

    // ============= CONTENT BROWSING FLOWS (API + IMAGE-BASED UNLOCK) =============
    // These flows return API data with real-time unlock status for browsing

    val themeCategories: Flow<List<ThemeCategory>> = flow {
        val result = getThemeCategoriesWithUnlockStatus()
        when (result) {
            is ApiResult.Success -> emit(result.data)
            else -> emit(emptyList())
        }
    }

    val widgetCategories: Flow<List<WidgetCategory>> = flow {
        val result = getWidgetCategoriesWithUnlockStatus()
        when (result) {
            is ApiResult.Success -> emit(result.data)
            else -> emit(emptyList())
        }
    }

    val iconCategories: Flow<List<IconCategory>> = flow {
        val result = getIconCategoriesWithUnlockStatus()
        when (result) {
            is ApiResult.Success -> emit(result.data)
            else -> emit(emptyList())
        }
    }

    // ============= USER LIBRARY FLOWS (DATABASE ONLY) =============
    // These flows return user's personal library for ProfileScreen

    val userUnlockedThemes: Flow<List<Theme>> = userLibraryRepository.getUserUnlockedThemes()
    val userPurchasedWallpaperPacks: Flow<List<WallpaperPack>> = userLibraryRepository.getUserPurchasedWallpaperPacks()
    val userPurchasedWidgetPacks: Flow<List<WidgetPack>> = userLibraryRepository.getUserPurchasedWidgetPacks()
    val userPurchasedIconPacks: Flow<List<IconPack>> = userLibraryRepository.getUserPurchasedIconPacks()

    // ============= CONTENT BROWSING METHODS (API + IMAGE-BASED UNLOCK) =============
    // These methods return API data with real-time unlock status for browsing

    /**
     * Get theme categories with unlock status for browsing
     * Uses API data + image-based unlock detection
     */
    suspend fun getThemeCategoriesWithUnlockStatus(): ApiResult<List<ThemeCategory>> {
        return try {
            val response = apiService.getThemes().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    // Convert API to domain models - pure API data without unlock status
                    // Unlock status will be determined dynamically via ImageBasedUnlockRepository
                    val themeCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toThemeCategory(categoryIndex)
                    }

                    ApiResult.Success(themeCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get widget categories with unlock status for browsing
     */
    suspend fun getWidgetCategoriesWithUnlockStatus(): ApiResult<List<WidgetCategory>> {
        return try {
            val response = apiService.getWidgetPacks().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val widgetCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toWidgetCategory(categoryIndex)
                    }
                    ApiResult.Success(widgetCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get icon categories with unlock status for browsing
     */
    suspend fun getIconCategoriesWithUnlockStatus(): ApiResult<List<IconCategory>> {
        return try {
            val response = apiService.getIconPacks().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val iconCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toIconCategory(categoryIndex)
                    }
                    ApiResult.Success(iconCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    // ============= PURCHASE/UNLOCK METHODS =============
    // These methods handle user purchases and update both image-based unlock and database

    /**
     * Purchase/unlock a theme
     * Updates both image-based unlock status and user's library database
     */
    suspend fun purchaseTheme(
        title: String,
        description: String,
        previewImage: String,
        categoryId: Int = 0
    ): Boolean {
        return try {
            // Mark as unlocked using image-based detection
            val imageUnlockSuccess = imageBasedUnlockRepository.markContentAsUnlocked(previewImage)

            if (imageUnlockSuccess) {
                // Add to user's library database
                userLibraryRepository.addThemeToUserLibrary(
                    title = title,
                    description = description,
                    previewImage = previewImage,
                    categoryId = categoryId
                )
                true
            } else {
                false
            }
        } catch (e: Exception) {
            android.util.Log.e("ThemeRepository", "Error purchasing theme: $title", e)
            false
        }
    }

    /**
     * Purchase a wallpaper pack
     */
    suspend fun purchaseWallpaperPack(
        title: String,
        previewImage: String,
        weight: Int,
        coin: Int,
        themeId: Int = 0
    ): Boolean {
        return try {
            val imageUnlockSuccess = imageBasedUnlockRepository.markContentAsUnlocked(previewImage)

            if (imageUnlockSuccess) {
                userLibraryRepository.addWallpaperPackToUserLibrary(
                    title = title,
                    previewImage = previewImage,
                    weight = weight,
                    coin = coin,
                    themeId = themeId
                )
                true
            } else {
                false
            }
        } catch (e: Exception) {
            android.util.Log.e("ThemeRepository", "Error purchasing wallpaper pack: $title", e)
            false
        }
    }

    /**
     * Purchase a widget pack
     */
    suspend fun purchaseWidgetPack(
        title: String,
        previewImage: String,
        weight: Int,
        coin: Int,
        type: com.amobear.themepack.data.model.WidgetType,
        categoryId: Int = 0
    ): Boolean {
        return try {
            val imageUnlockSuccess = imageBasedUnlockRepository.markContentAsUnlocked(previewImage)

            if (imageUnlockSuccess) {
                userLibraryRepository.addWidgetPackToUserLibrary(
                    title = title,
                    previewImage = previewImage,
                    weight = weight,
                    coin = coin,
                    type = type,
                    categoryId = categoryId
                )
                true
            } else {
                false
            }
        } catch (e: Exception) {
            android.util.Log.e("ThemeRepository", "Error purchasing widget pack: $title", e)
            false
        }
    }

    /**
     * Purchase an icon pack
     */
    suspend fun purchaseIconPack(
        title: String,
        previewImage: String,
        weight: Int,
        coin: Int,
        categoryId: Int = 0
    ): Boolean {
        return try {
            val imageUnlockSuccess = imageBasedUnlockRepository.markContentAsUnlocked(previewImage)

            if (imageUnlockSuccess) {
                userLibraryRepository.addIconPackToUserLibrary(
                    title = title,
                    previewImage = previewImage,
                    weight = weight,
                    coin = coin,
                    categoryId = categoryId
                )
                true
            } else {
                false
            }
        } catch (e: Exception) {
            android.util.Log.e("ThemeRepository", "Error purchasing icon pack: $title", e)
            false
        }
    }

    // ============= LEGACY METHODS (for backward compatibility) =============

    /**
     * Get all theme categories and themes with unlock status (legacy method)
     * Now uses the hybrid approach: API + image-based unlock detection
     */
    suspend fun getThemeCategories(forceRefresh: Boolean = false): ApiResult<List<ThemeCategory>> {
        return getThemeCategoriesWithUnlockStatus()
    }

    /**
     * Get all widget categories and widget packs (legacy method)
     */
    suspend fun getWidgetCategories(forceRefresh: Boolean = false): ApiResult<List<WidgetCategory>> {
        return getWidgetCategoriesWithUnlockStatus()
    }

    /**
     * Get all icon categories and icon packs (legacy method)
     */
    suspend fun getIconCategories(forceRefresh: Boolean = false): ApiResult<List<IconCategory>> {
        return getIconCategoriesWithUnlockStatus()
    }

    // ============= UTILITY METHODS =============

    /**
     * Check if user has any unlocked content (for empty states, onboarding)
     */
    suspend fun hasAnyUnlockedContent(): Boolean {
        val hasImageUnlocks = imageBasedUnlockRepository.hasUnlockedContent()
        val hasDbUnlocks = userLibraryRepository.hasAnyUnlockedContent()
        return hasImageUnlocks || hasDbUnlocks
    }

    /**
     * Clean up orphaned unlock markers
     * Should be called periodically to remove unlock status for content that no longer exists
     */
    suspend fun cleanupOrphanedUnlocks() {
        try {
            // Get all current valid image URLs from API
            val validImageUrls = mutableSetOf<String>()

            // Collect theme preview images
            val themesResult = getThemeCategoriesWithUnlockStatus()
            if (themesResult is ApiResult.Success) {
                themesResult.data.forEach { category ->
                    category.themes.forEach { theme ->
                        validImageUrls.add(theme.previewImage)
                        theme.wallpaperPacks.forEach { pack -> validImageUrls.add(pack.previewImage) }
                        theme.widgetPacks.forEach { pack -> validImageUrls.add(pack.previewImage) }
                        theme.iconPacks.forEach { pack -> validImageUrls.add(pack.previewImage) }
                    }
                }
            }

            // Collect widget pack preview images
            val widgetsResult = getWidgetCategoriesWithUnlockStatus()
            if (widgetsResult is ApiResult.Success) {
                widgetsResult.data.forEach { category ->
                    category.widgetPacks.forEach { pack -> validImageUrls.add(pack.previewImage) }
                }
            }

            // Collect icon pack preview images
            val iconsResult = getIconCategoriesWithUnlockStatus()
            if (iconsResult is ApiResult.Success) {
                iconsResult.data.forEach { category ->
                    category.iconPacks.forEach { pack -> validImageUrls.add(pack.previewImage) }
                }
            }

            // Clean up orphaned unlocks
            imageBasedUnlockRepository.cleanupOrphanedUnlocks(validImageUrls)

        } catch (e: Exception) {
            android.util.Log.e("ThemeRepository", "Error cleaning up orphaned unlocks", e)
        }
    }

    /**
     * Clear all user data (for logout, reset, etc.)
     */
    suspend fun clearAllUserData() {
        try {
            // Clear database library
            userLibraryRepository.clearUserLibrary()

            // Clear image-based unlocks
            val unlockedUrls = imageBasedUnlockRepository.getUnlockedContentUrls()
            unlockedUrls.forEach { url ->
                imageBasedUnlockRepository.markContentAsLocked(url)
            }
        } catch (e: Exception) {
            android.util.Log.e("ThemeRepository", "Error clearing user data", e)
        }
    }
}