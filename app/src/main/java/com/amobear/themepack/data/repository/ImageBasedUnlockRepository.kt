package com.amobear.themepack.data.repository

import com.amobear.themepack.utils.ImageCacheManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing unlock state using image-based detection
 * 
 * Core Principle: Content is unlocked if its preview image is marked as unlocked
 * This provides a simple, reliable way to track unlock status without complex data merging
 */
@Singleton
class ImageBasedUnlockRepository @Inject constructor(
    private val imageCacheManager: ImageCacheManager
) {
    
    /**
     * Check if content is unlocked based on its preview image URL
     * @param previewImageUrl The preview image URL from API
     * @return true if content is unlocked, false otherwise
     */
    fun isContentUnlocked(previewImageUrl: String): Boolean {
        return imageCacheManager.isUnlocked(previewImageUrl)
    }
    
    /**
     * Mark content as unlocked by marking its preview image as unlocked
     * This should be called when user purchases/unlocks content
     * @param previewImageUrl The preview image URL from API
     * @return true if successfully marked as unlocked
     */
    suspend fun markContentAsUnlocked(previewImageUrl: String): Boolean {
        return imageCacheManager.markAsUnlocked(previewImageUrl)
    }
    
    /**
     * Remove unlock status for content
     * @param previewImageUrl The preview image URL from API
     * @return true if successfully marked as locked
     */
    suspend fun markContentAsLocked(previewImageUrl: String): Boolean {
        return imageCacheManager.markAsLocked(previewImageUrl)
    }
    
    /**
     * Get all unlocked content preview image URLs
     * Useful for bulk operations or debugging
     * @return List of unlocked preview image URLs
     */
    fun getUnlockedContentUrls(): List<String> {
        return imageCacheManager.getUnlockedImageUrls()
    }
    
    /**
     * Check if user has any unlocked content
     * Useful for showing empty states or onboarding
     * @return true if user has unlocked any content
     */
    fun hasUnlockedContent(): Boolean {
        return imageCacheManager.getUnlockedImageUrls().isNotEmpty()
    }
    
    /**
     * Get unlocked content grouped by category for offline access
     * @return Map of category names to unlocked images
     */
    fun getUnlockedContentByCategory(): Map<String, List<com.amobear.themepack.utils.UnlockedImage>> {
        return imageCacheManager.getUnlockedImagesByCategory()
    }
    
    /**
     * Clean up orphaned unlock markers
     * Should be called periodically to remove unlock status for content that no longer exists in API
     * @param validImageUrls Set of current valid image URLs from API
     */
    suspend fun cleanupOrphanedUnlocks(validImageUrls: Set<String>) {
        val unlockedUrls = getUnlockedContentUrls()
        unlockedUrls.forEach { url ->
            if (!validImageUrls.contains(url)) {
                markContentAsLocked(url)
            }
        }
    }
}
