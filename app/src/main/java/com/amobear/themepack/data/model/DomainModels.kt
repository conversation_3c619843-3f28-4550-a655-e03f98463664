package com.amobear.themepack.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

// ============= THEME DOMAIN MODELS =============

@Parcelize
data class ThemeCategory(
    val id: Int,
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val themes: List<Theme> = emptyList()
) : Parcelable

@Parcelize
data class Theme(
    val id: Int,
    val title: String,
    val description: String,
    val previewImage: String,
    val categoryId: Int,
    val wallpaperPacks: List<WallpaperPack> = emptyList(),
    val widgetPacks: List<WidgetPack> = emptyList(),
    val iconPacks: List<IconPack> = emptyList()
) : Parcelable

@Parcelize
data class WallpaperPack(
    val id: Int,
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val wallpapers: List<Wallpaper> = emptyList()
) : Parcelable

@Parcelize
data class Wallpaper(
    val id: Int,
    val title: String,
    val imageUrl: String,
    val description: String,
    val packId: Int
) : Parcelable

// ============= WIDGET DOMAIN MODELS =============

@Parcelize
data class WidgetCategory(
    val id: Int,
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val widgetPacks: List<WidgetPack> = emptyList()
) : Parcelable

@Parcelize
data class WidgetPack(
    val id: Int,
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val categoryId: Int,
    val type: WidgetType,
    val widgets: List<Widget> = emptyList(),
    val compatibleThemes: List<CompatibleTheme> = emptyList()
) : Parcelable

@Parcelize
data class Widget(
    val id: Int,
    val title: String,
    val previewImage: String,
    val width: Int,
    val height: Int,
    val size: WidgetSize,
    val packId: Int,
    // Local state
    val isInstalled: Boolean = false,
    val localPath: String? = null,
    val configuration: Map<String, String> = emptyMap()
) : Parcelable

@Parcelize
enum class WidgetType : Parcelable {
    CLOCK,
    WEATHER,
    CALENDAR,
    FRAME;

    companion object {
        fun fromString(value: String): WidgetType {
            return when (value.lowercase()) {
                "clock" -> CLOCK
                "weather" -> WEATHER
                "calendar" -> CALENDAR
                "frame" -> FRAME
                else -> CLOCK
            }
        }
    }
}

@Parcelize
enum class WidgetSize : Parcelable {
    SMALL,    // 2x2
    MEDIUM,   // 4x2
    LARGE;    // 4x5

    companion object {
        fun fromString(value: String): WidgetSize {
            return when (value.lowercase()) {
                "small" -> SMALL
                "medium" -> MEDIUM
                "large" -> LARGE
                else -> SMALL
            }
        }
    }
}

// ============= ICON DOMAIN MODELS =============

@Parcelize
data class IconCategory(
    val id: Int,
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val iconPacks: List<IconPack> = emptyList()
) : Parcelable

@Parcelize
data class IconPack(
    val id: Int,
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val categoryId: Int,
    val icons: List<Icon> = emptyList(),
    val compatibleThemes: List<CompatibleTheme> = emptyList()
) : Parcelable

@Parcelize
data class Icon(
    val id: Int,
    val appId: String,
    val name: String,
    val imageUrl: String,
    val iconPackId: Int
) : Parcelable

// ============= SHARED MODELS =============

@Parcelize
data class CompatibleTheme(
    val id: Int,
    val title: String
) : Parcelable


// ============= UI STATE MODELS =============

@Parcelize
data class ThemeDownloadState(
    val themeId: String,
    val isDownloading: Boolean = false,
    val progress: Float = 0f,
    val error: String? = null
) : Parcelable

@Parcelize
data class PackPurchaseState(
    val packId: Int,
    val packType: PackType,
    val isPurchasing: Boolean = false,
    val isPurchased: Boolean = false,
    val error: String? = null
) : Parcelable

@Parcelize
enum class PackType : Parcelable {
    WALLPAPER,
    WIDGET,
    ICON
} 