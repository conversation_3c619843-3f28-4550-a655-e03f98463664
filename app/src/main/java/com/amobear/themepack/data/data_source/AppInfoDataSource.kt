package com.amobear.themepack.data.data_source

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import com.amobear.themepack.data.datalocal.device.AppInfoDto
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class AppInfoDataSource @Inject constructor(
    @ApplicationContext private val context: Context,
) {
    private val packageManager: PackageManager = context.packageManager

    suspend fun getInstalledApplications(): List<AppInfoDto> {
        val flags = PackageManager.GET_META_DATA or
                PackageManager.GET_SHARED_LIBRARY_FILES
        return packageManager.getInstalledApplications(flags).filter { appInfo ->
            (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) == 0
        }.mapNotNull { appInfo ->
            try {
                val packageInfo = packageManager.getPackageInfo(
                    appInfo.packageName,
                    PackageManager.GET_ACTIVITIES or PackageManager.GET_META_DATA
                )

                AppInfoDto(
                    packageName = appInfo.packageName,
                    appName = packageManager.getApplicationLabel(appInfo).toString(),
                    versionName = packageInfo.versionName ?: "",
                    versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                        packageInfo.longVersionCode
                    } else {
                        @Suppress("DEPRECATION")
                        packageInfo.versionCode.toLong()
                    },
                    firstInstallTime = packageInfo.firstInstallTime,
                    lastUpdateTime = packageInfo.lastUpdateTime,
                    icon = packageManager.getApplicationIcon(appInfo.packageName)
                )
            } catch (e: Exception) {
                null
            }
        }
    }

    fun getAppInfo(packageName: String): AppInfoDto? {
        return try {
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            val packageInfo = packageManager.getPackageInfo(
                packageName,
                PackageManager.GET_ACTIVITIES or PackageManager.GET_META_DATA
            )

            AppInfoDto(
                packageName = appInfo.packageName,
                appName = packageManager.getApplicationLabel(appInfo).toString(),
                versionName = packageInfo.versionName ?: "",
                versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode.toLong()
                },
                firstInstallTime = packageInfo.firstInstallTime,
                lastUpdateTime = packageInfo.lastUpdateTime,
                icon = packageManager.getApplicationIcon(appInfo.packageName)
            )
        } catch (e: Exception) {
            null
        }
    }
}