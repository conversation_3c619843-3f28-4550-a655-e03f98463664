package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.datalocal.ThemeEntity
import com.amobear.themepack.data.datalocal.WallpaperPackEntity
import com.amobear.themepack.data.datalocal.WidgetPackEntity
import com.amobear.themepack.data.datalocal.IconPackEntity
import com.amobear.themepack.data.mapper.toDomain
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.model.WidgetPack
import com.amobear.themepack.data.model.IconPack
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing user's personal library stored in database
 * 
 * Purpose: ONLY for tracking user's unlocked content for ProfileScreen
 * - Database entities represent user's purchased/unlocked content
 * - Used exclusively by ProfileScreen to show user's library
 * - NOT used for general content storage or API caching
 */
@Singleton
class UserLibraryRepository @Inject constructor(
    private val database: ThemeAppDatabase
) {
    
    // ============= USER'S UNLOCKED THEMES =============
    
    /**
     * Get user's unlocked themes from database
     * Used by ProfileScreen to show user's theme library
     */
    fun getUserUnlockedThemes(): Flow<List<Theme>> {
        return database.themeDao().getUnlockedThemes().map { entities ->
            entities.map { it.toDomain() }
        }
    }
    
    /**
     * Save theme to user's library when purchased/unlocked
     */
    suspend fun addThemeToUserLibrary(
        title: String,
        description: String,
        previewImage: String,
        categoryId: Int = 0
    ) {
        val themeEntity = ThemeEntity(
            id = title.hashCode().let { if (it < 0) -it else it }, // Generate positive ID
            title = title,
            description = description,
            previewImage = previewImage,
            categoryId = categoryId,
            downloadDate = System.currentTimeMillis() // Mark as unlocked
        )
        database.themeDao().insertTheme(themeEntity)
    }
    
    /**
     * Remove theme from user's library
     */
    suspend fun removeThemeFromUserLibrary(title: String) {
        val theme = database.themeDao().getThemeByTitle(title)
        theme?.let { database.themeDao().deleteTheme(it) }
    }
    
    // ============= USER'S PURCHASED WALLPAPER PACKS =============
    
    /**
     * Get user's purchased wallpaper packs from database
     */
    fun getUserPurchasedWallpaperPacks(): Flow<List<WallpaperPack>> {
        return database.wallpaperDao().getPurchasedWallpaperPacks().map { entities ->
            entities.map { it.toDomain() }
        }
    }
    
    /**
     * Save wallpaper pack to user's library when purchased
     */
    suspend fun addWallpaperPackToUserLibrary(
        title: String,
        previewImage: String,
        weight: Int,
        coin: Int,
        themeId: Int = 0
    ) {
        val packEntity = WallpaperPackEntity(
            id = title.hashCode().let { if (it < 0) -it else it },
            themeId = themeId,
            title = title,
            previewImage = previewImage,
            weight = weight,
            coin = coin,
            isPurchased = true // Mark as purchased
        )
        database.wallpaperDao().insertWallpaperPack(packEntity)
    }
    
    /**
     * Remove wallpaper pack from user's library
     */
    suspend fun removeWallpaperPackFromUserLibrary(title: String) {
        val pack = database.wallpaperDao().getWallpaperPackByTitle(title)
        pack?.let { 
            val updatedPack = it.copy(isPurchased = false)
            database.wallpaperDao().updateWallpaperPack(updatedPack)
        }
    }
    
    // ============= USER'S PURCHASED WIDGET PACKS =============
    
    /**
     * Get user's purchased widget packs from database
     */
    fun getUserPurchasedWidgetPacks(): Flow<List<WidgetPack>> {
        return database.widgetDao().getPurchasedWidgetPacks().map { entities ->
            entities.map { it.toDomain() }
        }
    }
    
    /**
     * Save widget pack to user's library when purchased
     */
    suspend fun addWidgetPackToUserLibrary(
        title: String,
        previewImage: String,
        weight: Int,
        coin: Int,
        type: com.amobear.themepack.data.model.WidgetType,
        categoryId: Int = 0
    ) {
        val packEntity = WidgetPackEntity(
            id = title.hashCode().let { if (it < 0) -it else it },
            categoryId = categoryId,
            title = title,
            previewImage = previewImage,
            weight = weight,
            coin = coin,
            type = type,
            isPurchased = true // Mark as purchased
        )
        database.widgetDao().insertWidgetPack(packEntity)
    }
    
    /**
     * Remove widget pack from user's library
     */
    suspend fun removeWidgetPackFromUserLibrary(title: String) {
        val pack = database.widgetDao().getWidgetPackByTitle(title)
        pack?.let {
            val updatedPack = it.copy(isPurchased = false)
            database.widgetDao().updateWidgetPack(updatedPack)
        }
    }
    
    // ============= USER'S PURCHASED ICON PACKS =============
    
    /**
     * Get user's purchased icon packs from database
     */
    fun getUserPurchasedIconPacks(): Flow<List<IconPack>> {
        return database.iconDao().getPurchasedIconPacks().map { entities ->
            entities.map { it.toDomain() }
        }
    }
    
    /**
     * Save icon pack to user's library when purchased
     */
    suspend fun addIconPackToUserLibrary(
        title: String,
        previewImage: String,
        weight: Int,
        coin: Int,
        categoryId: Int = 0
    ) {
        val packEntity = IconPackEntity(
            id = title.hashCode().let { if (it < 0) -it else it },
            categoryId = categoryId,
            title = title,
            previewImage = previewImage,
            weight = weight,
            coin = coin,
            isPurchased = true // Mark as purchased
        )
        database.iconDao().insertIconPack(packEntity)
    }
    
    /**
     * Remove icon pack from user's library
     */
    suspend fun removeIconPackFromUserLibrary(title: String) {
        val pack = database.iconDao().getIconPackByTitle(title)
        pack?.let {
            val updatedPack = it.copy(isPurchased = false)
            database.iconDao().updateIconPack(updatedPack)
        }
    }
    
    // ============= UTILITY METHODS =============
    
    /**
     * Check if user has any content in their library
     */
    suspend fun hasAnyUnlockedContent(): Boolean {
        val themes = database.themeDao().getAllThemesList()
        val wallpaperPacks = database.wallpaperDao().getAllWallpaperPacks()
        val widgetPacks = database.widgetDao().getAllWidgetPacks()
        val iconPacks = database.iconDao().getAllIconPacks()
        
        return themes.isNotEmpty() || 
               wallpaperPacks.any { it.isPurchased } ||
               widgetPacks.any { it.isPurchased } ||
               iconPacks.any { it.isPurchased }
    }
    
    /**
     * Clear all user library data (for logout, reset, etc.)
     */
    suspend fun clearUserLibrary() {
        database.themeDao().deleteAllThemes()
        // Note: We don't delete packs, just mark as not purchased
        // This preserves the data structure while removing user ownership
    }
}
