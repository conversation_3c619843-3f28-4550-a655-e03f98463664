package com.amobear.themepack.data.data_source

import android.content.Context
import com.amobear.themepack.data.model.IconResponse
import com.squareup.moshi.Moshi
import dagger.hilt.android.qualifiers.ApplicationContext
import com.squareup.moshi.JsonDataException
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AssetIconDataSource @Inject constructor(
    @ApplicationContext private val context: Context,
    private val moshi: <PERSON><PERSON>
) {
    fun getIconsFromAsset(): Result<IconResponse> {
        return runCatching {
            Timber.tag("AssetIconDataSource").d("Starting to load icons from asset")

            val jsonString = context.assets.open("mock-icon-packs.json").bufferedReader().use { it.readText() }
            Timber.tag("AssetIconDataSource").d("Loaded JSON string, length: ${jsonString.length}")

            val adapter = moshi.adapter(IconResponse::class.java)
            val response = adapter.fromJson(jsonString)
                ?: throw Exception("Failed to parse icons.json - parsed response is null")

            response
        }.onFailure { exception ->
            when (exception) {
                is JsonDataException -> {
                    Timber.tag("AssetIconDataSource").e(exception, "JSON parsing error")
                }
                else -> {
                    Timber.tag("AssetIconDataSource").e(exception, "Error loading icons from asset")
                }
            }
        }
    }
}