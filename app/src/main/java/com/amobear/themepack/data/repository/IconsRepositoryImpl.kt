package com.amobear.themepack.data.repository

import com.amobear.themepack.data.data_source.AssetIconDataSource
import com.amobear.themepack.data.mapper.*
import com.amobear.themepack.domain.model.icon.CategoryItem
import com.amobear.themepack.domain.model.icon.IconSection
import com.amobear.themepack.domain.repository.IconsRepository
import kotlinx.collections.immutable.ImmutableList
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class IconsRepositoryImpl @Inject constructor(
    private val assetIconDataSource: AssetIconDataSource
) : IconsRepository {

    override suspend fun getCategories(): Result<ImmutableList<CategoryItem>> {
        return assetIconDataSource.getIconsFromAsset()
            .mapCatching { response ->
                response.getCategories()
            }
    }

    override suspend fun getIconSections(categoryId: String): Result<ImmutableList<IconSection>> {
        return assetIconDataSource.getIconsFromAsset()
            .mapCatching { response ->
                response.getIconSectionsForCategory(categoryId)
            }
    }

    override suspend fun getAllIconSections(): Result<ImmutableList<IconSection>> {
        return assetIconDataSource.getIconsFromAsset()
            .mapCatching { response ->
                response.getAllIconSections()
            }
    }
}