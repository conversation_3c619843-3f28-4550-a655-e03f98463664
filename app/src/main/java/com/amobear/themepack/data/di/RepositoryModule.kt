package com.amobear.themepack.data.di

import com.amobear.themepack.data.datalocal.IconCategoryDao
import com.amobear.themepack.data.datalocal.IconDao
import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.datalocal.ThemeCategoryDao
import com.amobear.themepack.data.datalocal.ThemeDao
import com.amobear.themepack.data.datalocal.WallpaperDao
import com.amobear.themepack.data.datalocal.WidgetCategoryDao
import com.amobear.themepack.data.datalocal.WidgetDao
import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import com.amobear.themepack.data.repository.DownloadedWallpaperRepository
import com.amobear.themepack.data.repository.DownloadedWallpaperRepositoryImpl
import com.amobear.themepack.data.repository.ThemeRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    @Provides
    @Singleton
    fun provideNewThemeDao(database: ThemeAppDatabase): ThemeDao {
        return database.themeDao()
    }

    @Provides
    @Singleton
    fun provideThemeCategoryDao(database: ThemeAppDatabase): ThemeCategoryDao {
        return database.themeCategoryDao()
    }

    @Provides
    @Singleton
    fun provideWallpaperDao(database: ThemeAppDatabase): WallpaperDao {
        return database.wallpaperDao()
    }

    @Provides
    @Singleton
    fun provideWidgetCategoryDao(database: ThemeAppDatabase): WidgetCategoryDao {
        return database.widgetCategoryDao()
    }

    @Provides
    @Singleton
    fun provideWidgetPackDao(database: ThemeAppDatabase): com.amobear.themepack.data.datalocal.WidgetPackDao {
        return database.widgetPackDao()
    }

    @Provides
    @Singleton
    fun provideWidgetDao(database: ThemeAppDatabase): WidgetDao {
        return database.widgetDao()
    }

    @Provides
    @Singleton
    fun provideIconCategoryDao(database: ThemeAppDatabase): IconCategoryDao {
        return database.iconCategoryDao()
    }

    @Provides
    @Singleton
    fun provideIconPackDao(database: ThemeAppDatabase): com.amobear.themepack.data.datalocal.IconPackDao {
        return database.iconPackDao()
    }

    @Provides
    @Singleton
    fun provideIconDao(database: ThemeAppDatabase): IconDao {
        return database.iconDao()
    }

    @Provides
    @Singleton
    fun provideImageBasedUnlockRepository(
        imageCacheManager: com.amobear.themepack.utils.ImageCacheManager
    ): com.amobear.themepack.data.repository.ImageBasedUnlockRepository {
        return com.amobear.themepack.data.repository.ImageBasedUnlockRepository(imageCacheManager)
    }

    @Provides
    @Singleton
    fun provideUserLibraryRepository(
        database: ThemeAppDatabase
    ): com.amobear.themepack.data.repository.UserLibraryRepository {
        return com.amobear.themepack.data.repository.UserLibraryRepository(database)
    }

    @Provides
    @Singleton
    fun provideNewThemeRepository(
        apiService: com.amobear.themepack.data.network.ApiService,
        imageBasedUnlockRepository: com.amobear.themepack.data.repository.ImageBasedUnlockRepository,
        userLibraryRepository: com.amobear.themepack.data.repository.UserLibraryRepository
    ): ThemeRepository {
        return ThemeRepository(
            apiService,
            imageBasedUnlockRepository,
            userLibraryRepository
        )
    }
    
    @Provides
    @Singleton
    fun provideDownloadedWallpaperRepository(
        preferenceProvider: SharePreferenceProvider
    ): DownloadedWallpaperRepository {
        return DownloadedWallpaperRepositoryImpl(preferenceProvider)
    }
}
