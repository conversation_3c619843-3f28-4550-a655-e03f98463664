package com.amobear.themepack.data.repository

import com.amobear.themepack.core.AppCoroutineDispatchers
import com.amobear.themepack.core.DefaultAppCoroutineDispatchers
import com.amobear.themepack.data.data_source.AppInfoDataSource
import com.amobear.themepack.data.mapper.toDomain
import com.amobear.themepack.domain.model.AppInfo
import com.amobear.themepack.domain.repository.AppRepository
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import okhttp3.Dispatcher
import javax.inject.Inject

class AppRepositoryImpl @Inject constructor(
    private val appInfoDataSource: AppInfoDataSource,
    private val dispatchers: AppCoroutineDispatchers = DefaultAppCoroutineDispatchers()
) : AppRepository {

    private var cachedApps: ImmutableList<AppInfo>? = null
    private var lastFetchTime = 0L
    private val cacheValidityMs = 30_000L

    override fun getInstalledApps(): Flow<ImmutableList<AppInfo>> = flow {
        val currentTime = System.currentTimeMillis()

        if (cachedApps != null && (currentTime - lastFetchTime) < cacheValidityMs) {
            emit(cachedApps ?: persistentListOf())
            return@flow
        }

        val apps = withContext(dispatchers.io) {
            appInfoDataSource.getInstalledApplications()
                .map { it.toDomain() }
                .sortedBy { it.appName }
                .toImmutableList()
        }

        cachedApps = apps
        lastFetchTime = currentTime
        emit(apps)
    }

    override suspend fun getAppInfo(packageName: String): AppInfo? {
        return appInfoDataSource.getAppInfo(packageName)?.toDomain()
    }
}