package com.amobear.themepack.data.datalocal.sharepref

import android.content.SharedPreferences
import com.amobear.themepack.data.model.AppLanguage
import com.squareup.moshi.FromJson
import com.squareup.moshi.Moshi
import com.squareup.moshi.ToJson
import javax.inject.Inject
import javax.inject.Singleton
import androidx.core.content.edit

@Singleton
class SharePreferenceProvider
@Inject
constructor(val sharedPreferences: SharedPreferences, val moshi: <PERSON>shi) {
    companion object {
        const val NAME_SHARE_PREFERENCE = "ThemePack"
        const val FIRST_TIME_OPEN_APP = "first_time_open_app"
        const val USER_COINS = "user_coins"
        const val KEY_AD_WATCHED_FOR_APP = "ad_watched_for_app_"
        const val LANGUAGE = "language"
    }

    @ToJson
    inline fun <reified T> save(key: String, value: Any) {
        val editor = sharedPreferences.edit()
        when (value) {
            is String -> editor.putString(key, value)
            is Float -> editor.putFloat(key, value)
            is Int -> editor.putInt(key, value)
            is Long -> editor.putLong(key, value)
            is Boolean -> editor.putBoolean(key, value)
            else -> {
                val adapter = moshi.adapter(value.javaClass)
                editor.putString(key, adapter.toJson(value))
            }
        }
        editor.apply()
    }

    @FromJson
    inline fun <reified T> get(key: String): T? {
        when (T::class) {
            Float::class -> return sharedPreferences.getFloat(key, 0f) as T
            Int::class -> return sharedPreferences.getInt(key, 0) as T
            Long::class -> return sharedPreferences.getLong(key, 0) as T
            String::class -> return sharedPreferences.getString(key, "") as T
            Boolean::class -> return sharedPreferences.getBoolean(key, false) as T
            else -> {
                val any = sharedPreferences.getString(key, "")
                val adapter = moshi.adapter(T::class.java)
                if (!any.isNullOrEmpty()) {
                    return adapter.fromJson(any)
                }
            }
        }
        return null
    }

    /**
     * Get user's current coin balance
     */
    fun getUserCoins(): Int {
        return sharedPreferences.getInt(USER_COINS, 99999) // Default 99999 coins
    }

  /**
   * Set user's coin balance
   */
  fun setUserCoins(coins: Int) {
    sharedPreferences.edit().putInt(USER_COINS, coins).apply()
  }

    /**
     * Add coins to user's balance
     */
    fun addUserCoins(coins: Int) {
        val currentCoins = getUserCoins()
        setUserCoins(currentCoins + coins)
    }

    /**
     * Deduct coins from user's balance
     */
    fun deductUserCoins(coins: Int): Boolean {
        val currentCoins = getUserCoins()
        return if (currentCoins >= coins) {
            setUserCoins(currentCoins - coins)
            true
        } else {
            false
        }
    }

    fun getSavedLanguage(): AppLanguage? {
        return AppLanguage.entries.find {
            it.locale == sharedPreferences.getString(LANGUAGE, null)
        }
    }

    fun setLanguage(language: AppLanguage) {
        sharedPreferences.edit { putString(LANGUAGE, language.locale) }
    }
}
