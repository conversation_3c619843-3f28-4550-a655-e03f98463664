package com.amobear.themepack.data.mapper

import com.amobear.themepack.data.datalocal.IconCategoryEntity
import com.amobear.themepack.data.datalocal.IconEntity
import com.amobear.themepack.data.datalocal.IconPackEntity
import com.amobear.themepack.data.datalocal.ThemeCategoryEntity
import com.amobear.themepack.data.datalocal.ThemeEntity
import com.amobear.themepack.data.datalocal.WallpaperEntity
import com.amobear.themepack.data.datalocal.WallpaperPackEntity
import com.amobear.themepack.data.datalocal.WidgetCategoryEntity
import com.amobear.themepack.data.datalocal.WidgetEntity
import com.amobear.themepack.data.datalocal.WidgetPackEntity
import com.amobear.themepack.data.model.Icon
import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.model.IconPack
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.Wallpaper
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.model.Widget
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.model.WidgetPack

// Theme Category mappings
fun ThemeCategoryEntity.toDomain(themes: List<Theme> = emptyList()): ThemeCategory {
    return ThemeCategory(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        themes = themes
    )
}

fun ThemeCategory.toEntity(): ThemeCategoryEntity {
    return ThemeCategoryEntity(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight
    )
}

// Theme mappings
fun ThemeEntity.toDomain(
    wallpaperPacks: List<WallpaperPack> = emptyList(),
    widgetPacks: List<WidgetPack> = emptyList(),
    iconPacks: List<IconPack> = emptyList()
): Theme {
    return Theme(
        id = id,
        title = title,
        description = description,
        previewImage = previewImage,
        categoryId = categoryId,
        wallpaperPacks = wallpaperPacks,
        widgetPacks = widgetPacks,
        iconPacks = iconPacks
    )
}

fun Theme.toEntity(): ThemeEntity {
    return ThemeEntity(
        id = id,
        title = title,
        description = description,
        previewImage = previewImage,
        categoryId = categoryId,
        downloadDate = System.currentTimeMillis() // Set current time when converting to entity (for user library)
    )
}

// Wallpaper mappings
fun WallpaperPackEntity.toDomain(wallpapers: List<Wallpaper> = emptyList()): WallpaperPack {
    return WallpaperPack(
        id = id,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        wallpapers = wallpapers
    )
}

fun WallpaperPack.toEntity(themeId: Int): WallpaperPackEntity {
    return WallpaperPackEntity(
        id = id,
        themeId = themeId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        isPurchased = true // Set as purchased when converting to entity (for user library)
    )
}

fun WallpaperEntity.toDomain(): Wallpaper {
    return Wallpaper(
        id = id,
        title = title,
        imageUrl = imageUrl,
        description = description,
        packId = packId
    )
}

fun Wallpaper.toEntity(): WallpaperEntity {
    return WallpaperEntity(
        id = id,
        packId = packId,
        title = title,
        imageUrl = imageUrl,
        description = description,
        localPath = null // Not storing local paths in domain models anymore
    )
}

fun WidgetCategoryEntity.toDomain(widgetPacks: List<WidgetPack> = emptyList()): WidgetCategory {
    return WidgetCategory(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        widgetPacks = widgetPacks
    )
}

fun WidgetCategory.toEntity(): WidgetCategoryEntity {
    return WidgetCategoryEntity(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight
    )
}

fun WidgetPackEntity.toDomain(
    widgets: List<Widget> = emptyList(),
    compatibleThemes: List<com.amobear.themepack.data.model.CompatibleTheme> = emptyList()
): WidgetPack {
    return WidgetPack(
        id = id,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = categoryId,
        type = type,
        widgets = widgets,
        compatibleThemes = compatibleThemes
    )
}

fun WidgetPack.toEntity(): WidgetPackEntity {
    return WidgetPackEntity(
        id = id,
        categoryId = categoryId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        type = type,
        isPurchased = true // Set as purchased when converting to entity (for user library)
    )
}

fun WidgetEntity.toDomain(): Widget {
    return Widget(
        id = id,
        title = title,
        previewImage = previewImage,
        width = width,
        height = height,
        size = size,
        packId = packId,
        isInstalled = isInstalled,
        localPath = localPath,
        configuration = configuration
    )
}

fun Widget.toEntity(): WidgetEntity {
    return WidgetEntity(
        id = id,
        packId = packId,
        title = title,
        previewImage = previewImage,
        width = width,
        height = height,
        size = size,
        isInstalled = isInstalled,
        localPath = localPath,
        configuration = configuration
    )
}

fun IconCategoryEntity.toDomain(iconPacks: List<IconPack> = emptyList()): IconCategory {
    return IconCategory(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        iconPacks = iconPacks
    )
}

fun IconCategory.toEntity(): IconCategoryEntity {
    return IconCategoryEntity(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight
    )
}

fun IconPackEntity.toDomain(
    icons: List<Icon> = emptyList(),
    compatibleThemes: List<com.amobear.themepack.data.model.CompatibleTheme> = emptyList()
): IconPack {
    return IconPack(
        id = id,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = categoryId,
        icons = icons,
        compatibleThemes = compatibleThemes
    )
}

fun IconPack.toEntity(): IconPackEntity {
    return IconPackEntity(
        id = id,
        categoryId = categoryId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        isPurchased = true // Set as purchased when converting to entity (for user library)
    )
}

fun IconEntity.toDomain(): Icon {
    return Icon(
        id = id,
        appId = appId,
        name = name,
        imageUrl = imageUrl,
        iconPackId = iconPackId
    )
}

fun Icon.toEntity(): IconEntity {
    return IconEntity(
        id = id,
        iconPackId = iconPackId,
        appId = appId,
        name = name,
        imageUrl = imageUrl,
        localPath = null // Not storing local paths in domain models anymore
    )
}
