package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DownloadedWallpaperRepositoryImpl @Inject constructor(
    private val preferenceProvider: SharePreferenceProvider
) : DownloadedWallpaperRepository {
    
    companion object {
        private const val KEY_UNLOCKED_WALLPAPERS = "unlocked_wallpapers"
    }
    
    override fun getUnlockedWallpapers(): Flow<List<DownloadedWallpaper>> = flow {
        val wallpapersString = preferenceProvider.get<String>(KEY_UNLOCKED_WALLPAPERS) ?: ""
        val wallpapers = if (wallpapersString.isNotEmpty()) {
            wallpapersString.split(";").map { url ->
                DownloadedWallpaper(url)
            }
        } else {
            emptyList()
        }
        emit(wallpapers)
    }
    
    override suspend fun saveUnlockedWallpaper(wallpaperUrl: String) {
        val currentWallpapers = preferenceProvider.get<String>(KEY_UNLOCKED_WALLPAPERS) ?: ""
        val updatedWallpapers = if (currentWallpapers.isEmpty()) {
            wallpaperUrl
        } else {
            if (!currentWallpapers.contains(wallpaperUrl)) {
                "$currentWallpapers;$wallpaperUrl"
            } else {
                currentWallpapers
            }
        }
        preferenceProvider.save<String>(KEY_UNLOCKED_WALLPAPERS, updatedWallpapers)
    }
    
    override suspend fun isWallpaperUnlocked(wallpaperUrl: String): Boolean {
        val wallpapersString = preferenceProvider.get<String>(KEY_UNLOCKED_WALLPAPERS) ?: ""
        return wallpapersString.contains(wallpaperUrl)
    }
} 